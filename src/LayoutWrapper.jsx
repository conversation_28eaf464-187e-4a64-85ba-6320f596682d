import React, { memo } from 'react'
import useAuthStore from './store/useAuthStore'

const LayoutWrapper = ({ children }) => {
  const { pathCookieCheck } = useAuthStore()

  return pathCookieCheck ? children : null
}

// Memoize LayoutWrapper to prevent unnecessary re-renders
// LayoutWrapper re-renders when:
// - pathCookieCheck state changes
// - children prop changes
const MemoizedLayoutWrapper = memo(LayoutWrapper, (prevProps, nextProps) => {
  // Compare children prop for shallow equality
  return prevProps.children === nextProps.children
})

export default MemoizedLayoutWrapper
