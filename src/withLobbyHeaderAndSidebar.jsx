import { matchPath } from 'react-router-dom';

const allowedSidebarPaths = [
  '/',
  '/lobby',
  '/referral',
  '/user/verifyEmail',
  '/user/forgotPassword',
  '/user/store',
  '/user/account-details',
  '/user/account-details/vip-player-interests',
  '/user/account-details/responsible-gaming',
  '/bets',
  '/affiliate',
  '/user/reward',
  '/geoblock',
  '/comingsoon',
  '/cms/:cmsPage',
  '/responsible-gambling',
  '/promotions-page',
  '/user/store/deposit-status',
  '/refer-friend',
  '/tournaments-page',
  '/tournament-detail/:tournamentType/:tournamentId',
  '/tier',
  '/settings',
  '/jackpot',
  '/refer-a-friend',
  '/real-money-casino-games',
  '/real-money-casino-slots',
  '/live-dealer-casino',
  '/instant-win-games',
  '/casino-table-games',
  '/online-casinos-bonuses',
  '/casino/california',
  '/casino/florida',
  '/casino/texas',
  '/hall-of-fame',
  '/subscriptions'
];

const allowedHeaderPaths = [
  '/',
  '/lobby',
  '/referral',
  '/user/verifyEmail',
  '/user/forgotPassword',
  '/user/store',
  '/user/account-details',
  '/user/account-details/vip-player-interests',
  '/user/account-details/responsible-gaming',
  '/game-play/:gameId',
  '/game-play-tournament/:gameId/:tournamentId',
  '/bets',
  '/affiliate',
  '/user/reward',
  '/geoblock',
  '/comingsoon',
  '/cms/:cmsPage',
  '/responsible-gambling',
  '/promotions-page',
  '/user/store/deposit-status',
  '/refer-friend',
  '/tournaments-page',
  '/tournament-detail/:tournamentType/:tournamentId',
  '/tier',
  '/settings',
  '/jackpot',
  '/refer-a-friend',
  '/hall-of-fame',
  '/subscriptions'
];

export const shouldShowSideBarAndFooter = (pathname) => {
  return allowedSidebarPaths.some((path) =>
    matchPath({ path, end: true }, pathname)
  );
};

export const shouldShowHeader = (pathname) => {
  return allowedHeaderPaths.some((path) =>
    matchPath({ path, end: true }, pathname)
  );
};
