import React, { useEffect } from 'react'
import useAuthStore from './store/useAuthStore'
import { useLocation, useNavigate } from 'react-router-dom'
import Loader from './components/Loader'

const LandingWrapper = ({ children }) => {
  const navigate = useNavigate()
  const { pathCookieCheck } = useAuthStore()
  const location = useLocation()
  const searchParams = location.search

  useEffect(() => {
    if (!pathCookieCheck) {
      navigate(`/online-social-casino-games${searchParams}`, { replace: true })
    }
  }, [pathCookieCheck, navigate, searchParams])

  if (pathCookieCheck) {
    return children
  }

  return <Loader />
}

export default LandingWrapper