import { lazy, Suspense } from 'react'
import LandingWrapper from './LandingWrapper'
import { PlayerRoutes } from './routes'
import Loader from './components/Loader'
import RouteSuspense from './components/RouteSuspense'

// Critical components that should load immediately (small or essential for initial render)
import NotFoundPage from './pages/NotFoundPage'

// Lazy load all non-critical components for better code splitting
// Group 1: SEO Landing Pages (public pages)
const GamesPage = lazy(() => import('./components/SeoLandingPages/GamesPage'))
const BlogPage = lazy(() => import('./components/SeoLandingPages/BlogSection/BlogPage'))
const FAQ = lazy(() => import('./components/SeoLandingPages/FAQ Section/faq'))
const ContactUsPage = lazy(() => import('./components/SeoLandingPages/ContactUs/ContactUsPage'))
const AboutUsPage = lazy(() => import('./components/SeoLandingPages/AboutUs/AboutUsPage'))
const DynamicBlog = lazy(() => import('./components/SeoLandingPages/BlogSection/DynamicBlog'))
const SeoGamePageWrapper = lazy(() => import('./SeoGamePageWrapper'))

// Group 2: User Account & Profile Components
const ProfileSection = lazy(() => import('./pages/Accounts/components/ProfileSection'))
const DynamicVipQuestionForm = lazy(() => import('./pages/Accounts/components/DynamicVipForm/index'))

// Group 3: Utility & Error Pages
const GeoBlock = lazy(() => import('./pages/GeoBlock'))
const ComingSoon = lazy(() => import('./pages/ComingSoon'))
const NotAvailable = lazy(() => import('./pages/NotAvailable'))

// Group 4: Feature Pages
const ReferPage = lazy(() => import('./pages/ReferPage'))
const HallOfFame = lazy(() => import('./pages/HallOfFame'))
const ResponsibleGambling = lazy(() => import('./pages/ResponsibleGambling'))
const Jackpot = lazy(() => import('./pages/Jackpot'))

// Group 5: Wrappers (keep non-lazy as they're lightweight wrappers)
import ReferralWrapper from './ReferralWrapper'
import VipQuestionWrapper from './VipQuestionWrapper'

// Group 6: Core functionality (already lazy-loaded)
const Package = lazy(() => import('./pages/Package'))
const Reward = lazy(() => import('./pages/Reward/Reward'))

const Subscriptions = lazy(() => import('./pages/Subscription'))

// Lazy imports
const Affiliates = lazy(() => import('./pages/Affiliates'))
const Lobby = lazy(() => import('./pages/Lobby/Lobby'))
const PromotionsPage = lazy(() => import('./pages/PromotionsPage'))
const BetHistorySection = lazy(() => import('./pages/Accounts/components/BetHistorySection'))
const ReferFriend = lazy(() => import('./pages/ReferFriend'))
const TournamentsPage = lazy(() => import('./pages/TournamentsPage/index'))
const Tier = lazy(() => import('./pages/Tier/index'))
const TournamentDetail = lazy(() => import('./pages/TournamentsPage/TournamentDetail'))
const Accounts = lazy(() => import('./pages/Accounts'))
const Cms = lazy(() => import('./pages/Cms/Cms'))
const GamePlay = lazy(() => import('./pages/GamePlay/GamePlay'))
const Landing = lazy(() => import('./pages/Landing/index'))
const Settings = lazy(() => import('./pages/Settings/index'))
const RedeemSuccessPopup = lazy(() => import('./components/StepperForm/RedeemSuccessPopup'))
const RedeemFailedPopup = lazy(() => import('./components/StepperForm/RedeemFailedPopup'))

const appRoutes = [
  {
    path: PlayerRoutes.DefaultRoute,
    element: (
      <LandingWrapper>
        <Lobby /> {/* Assuming Lobby is critical and not lazy-loaded */}
      </LandingWrapper>
    ) // Assuming LandingWrapper is critical and not lazy-loaded
  },
  {
    path: PlayerRoutes.Account,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <Accounts />
        </LandingWrapper>
      </Suspense>
    ),
    level: [
      {
        path: '',
        private: true,
        element: (
          <Suspense fallback={<Loader />}>
            <ProfileSection />
          </Suspense>
        )
      },
      {
        path: PlayerRoutes.UserResponsibleGaming,
        private: true,
        element: (
          <Suspense fallback={<Loader />}>
            <ResponsibleGambling />
          </Suspense>
        )
      },
      {
        path: PlayerRoutes.VipPlayerInterests,
        private: true,
        element: (
          <VipQuestionWrapper>
            <Suspense fallback={<Loader />}>
              <DynamicVipQuestionForm />
            </Suspense>
          </VipQuestionWrapper>
        )
      }
    ]
  },
  {
    path: PlayerRoutes.Landing,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.RedeemSuccess,
    element: (
      <Suspense fallback={<Loader />}>
        <RedeemSuccessPopup />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.RedeemFailed,
    element: (
      <Suspense fallback={<Loader />}>
        <RedeemFailedPopup />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Lobby,
    element: (
      <Suspense fallback={<Loader />}>
        <Lobby />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.VerifyEmail,
    element: (
      <Suspense fallback={<Loader />}>
        <Lobby />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Referral,
    element: (
      <ReferralWrapper>
        <Suspense fallback={<Loader />}>
          <Landing />
        </Suspense>
      </ReferralWrapper>
    )
  },
  {
    path: PlayerRoutes.ForgotPassword,
    element: (
      <Suspense fallback={<Loader />}>
        <Lobby />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Store,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <Package />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.DepositStatus,
    element: (
      <Suspense fallback={<Loader />}>
        <Package />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.GamePlay,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <GamePlay />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Reward,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <Reward />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Geoblock,
    element: (
      <Suspense fallback={<Loader />}>
        <GeoBlock />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.ComingSoon,
    element: (
      <Suspense fallback={<Loader />}>
        <ComingSoon />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Cms,
    element: (
      <Suspense fallback={<Loader />}>
        <Cms />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.ResponsibleGambling,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <ResponsibleGambling />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.PromotionsPage,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <PromotionsPage />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Bets,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <BetHistorySection />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Affiliate,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <Affiliates />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.ReferFriend,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <ReferFriend />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.TournamentsPage,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <TournamentsPage />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.TournamentsDetail,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <TournamentDetail />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.GamePlayTournament,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <GamePlay />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Tier,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <Tier />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Settings,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <Settings />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.ReferPage,
    element: (
      <Suspense fallback={<Loader />}>
        <ReferPage />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.HallOfFame,
    element: (
      <Suspense fallback={<Loader />}>
        <HallOfFame />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.RealMoneyCasinoGames,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.RealMoneyCasinoSlots,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.LiveDealerCasino,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.InstantWinGame,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.CasinoTableGame,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.OnlineCasinosBonus,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.CasinoCalifornio,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.CasinoFlorida,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.CasinoTexas,
    element: (
      <Suspense fallback={<Loader />}>
        <Landing />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.NotAvailable,
    element: (
      <Suspense fallback={<Loader />}>
        <NotAvailable />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.seoDynamicGamePageUrl,
    element: (
      <Suspense fallback={<Loader />}>
        <SeoGamePageWrapper />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Games,
    element: (
      <RouteSuspense routeName='Games Page'>
        <GamesPage />
      </RouteSuspense>
    )
  },
  {
    path: PlayerRoutes.Blog,
    element: (
      <RouteSuspense routeName='Blog Page'>
        <BlogPage />
      </RouteSuspense>
    )
  },
  {
    path: PlayerRoutes.PaymentStatus,
    element: (
      <Suspense fallback={<Loader />}>
        <Package />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.TrustlyRedeem,
    element: (
      <Suspense fallback={<Loader />}>
        <RedeemSuccessPopup />
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.FAQ,
    element: (
      <RouteSuspense routeName='FAQ Page'>
        <FAQ />
      </RouteSuspense>
    )
  },
  {
    path: PlayerRoutes.ContactUs,
    element: (
      <RouteSuspense routeName='Contact Us Page'>
        <ContactUsPage />
      </RouteSuspense>
    )
  },
  {
    path: PlayerRoutes.AboutUs,
    element: (
      <RouteSuspense routeName='About Us Page'>
        <AboutUsPage />
      </RouteSuspense>
    )
  },
  {
    path: PlayerRoutes.Jackpot,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <LandingWrapper>
          <Jackpot />
        </LandingWrapper>
      </Suspense>
    )
  },
  {
    path: PlayerRoutes.Subscriptions,
    private: true,
    element: (
      <Suspense fallback={<Loader />}>
        <Subscriptions />
      </Suspense>
    )
  },
  { path: PlayerRoutes.DynamicBlog, element: <DynamicBlog /> }, // Keep if small or critical
  { path: '*', element: <NotFoundPage /> } // Keep if small or critical
]

export default appRoutes
