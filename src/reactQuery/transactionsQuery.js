import { useQuery } from '@tanstack/react-query'

import { KeyTypes } from './KeyTypes'
import {
  getTransactions, getBetTransactions, getPaymentStatus, getCancelRedemption
} from '../utils/apiCalls'

const getTransactionsQuery = ({ params, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_TRANSACTIONS, params?.page, params?.startDate, params?.endDate, params?.actionType, params?.status],
    queryFn: () => {
      return getTransactions(params)
    },
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    retry: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
  })
}

const getRedeemTransactionsQuery = ({ params, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_REDEEM_REQUESTS, params?.page, params?.startDate, params?.endDate, params?.status],
    queryFn: () => {
      return getCancelRedemption(params)
    },
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    staleTime: 0,
    refetchOnReconnect: false,
    retry: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    },
  })
}

const getBetTransactionsQuery = ({ params, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_BET_TRANSACTIONS, params?.page, params?.limit, params?.startDate, params?.endDate, params?.coinType],
    queryFn: () => {
      return getBetTransactions(params)
    },
    select: (data) => {
      return data?.data?.data || {}
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

const getPaymentStatusQuery = ({ params, enabled, successToggler, errorToggler }) => {
  return useQuery({
    queryKey: [KeyTypes.GET_PAYMENT_STATUS, params?.providerType, params?.paymentReference],
    queryFn: () => {
      return getPaymentStatus(params)
    },
    select: (data) => {
      return data?.data || {}
    },
    enabled : enabled,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      successToggler && successToggler(data)
    },
    onError: (error) => {
      errorToggler && errorToggler(error)
    }
  })
}

export const packageQuery = {
  getTransactionsQuery,
  getBetTransactionsQuery,
  getPaymentStatusQuery,
  getRedeemTransactionsQuery
}
export default packageQuery
