import React, { Suspense, lazy, useEffect, useState, useMemo, memo } from 'react'
import { Routes, Route, Navigate, useLocation, useNavigate } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { usePortalStore, useUserStore } from './store/store'
import Loader from './components/Loader'
import './App.css'
import { Grid } from '@mui/material'
import useStyles from './MainPage.styles'
import {
  deleteAccessTokenCookies,
  deleteCookie,
  getCookie,
  setCookie,
  setReferralSocialCookie,
  setreferralCode
} from './utils/cookiesCollection'
import LayoutWrapper from './LayoutWrapper'
import { useDynamoKey } from './reactQuery'
import { shouldShowHeader, shouldShowSideBarAndFooter } from './withLobbyHeaderAndSidebar'
import appRoutes from './appRoutes'
import Header from './components/Header'
import SideBar from './components/SideBar'
import useAuthStore from './store/useAuthStore'

const Footer = lazy(() => import('./components/Footer'))

// Memoized layout components to prevent unnecessary re-renders
const MemoizedHeaderLayout = memo(({ showHeader }) =>
  showHeader ? (
    <LayoutWrapper>
      <Header />
    </LayoutWrapper>
  ) : null
)

const MemoizedSideBarLayout = memo(({ showSidebarAndFooter }) =>
  showSidebarAndFooter ? (
    <LayoutWrapper>
      <SideBar />
    </LayoutWrapper>
  ) : null
)

const MemoizedFooterLayout = memo(({ showSidebarAndFooter }) =>
  showSidebarAndFooter ? (
    <LayoutWrapper>
      <Footer />
    </LayoutWrapper>
  ) : null
)

let isUserAuthenticate = localStorage.getItem('username')

export const buildRouter = (routesConfig) => {
  // ===== GLOBAL CONSTANTS AND HOOKS =====
  // React Router hooks
  const location = useLocation()
  const pathname = location.pathname
  const navigate = useNavigate()

  // Styling hooks
  const classes = useStyles()

  // Store subscriptions - authentication state
  const isAuthenticate = useUserStore((state) => state.isAuthenticate)

  // Store subscriptions - portal and user management
  const portalClosePortal = usePortalStore((state) => state.closePortal)
  const userLogout = useUserStore((state) => state.logout)
  const userIsVipApproved = useUserStore((state) => state.isVipApproved)
  const userDetailsEmail = useUserStore((state) => state.userDetails?.email)
  const userDetails = useUserStore((state) => state.userDetails)
  const { pathCookieCheck } = useAuthStore()
  // State management
  const [redirectHandled, setRedirectHandled] = useState(false)
  const [socketRibbonData, setSocketData] = useState(() => {
    const storedData = getCookie('socketRibbonData')
    return storedData ? JSON.parse(storedData) : null
  })

  // Cookie and access token constants
  const accessCookie = getCookie('accessToken')

  // ===== MEMOIZED COMPUTATIONS =====
  // Memoize URL parameter processing to prevent repeated parsing
  const urlParams = useMemo(() => {
    const params = new URLSearchParams(window.location.search)
    const paramsData = {}
    params.forEach((value, key) => {
      paramsData[key] = value
    })
    return {
      params,
      paramsData,
      refferalKey: params.get('referralcode'),
      dynamoKey: params.get('d10x_link_id')
    }
  }, [location.search])

  const { params, paramsData, refferalKey, dynamoKey } = urlParams

  // Memoize conditional rendering logic to prevent unnecessary recalculations
  const showHeader = useMemo(() => shouldShowHeader(pathname), [pathname])
  const showSidebarAndFooter = useMemo(() => shouldShowSideBarAndFooter(pathname), [pathname])

  // Memoize route configuration processing to prevent repeated array operations
  const routeConfig = useMemo(() => {
    const privateRoutes = routesConfig.filter((route) => route.private).map((route) => route.path)
    const publicRoutes = routesConfig.filter((route) => !route.private).map((route) => route.path)
    return { privateRoutes, publicRoutes }
  }, [routesConfig])

  const { privateRoutes } = routeConfig

  // Memoize path-related calculations to prevent repeated string operations
  const pathInfo = useMemo(() => {
    const searchParams = location.search
    const currentPath = pathname.endsWith('/')
      ? `${pathname.slice(0, -1)}${searchParams}`
      : `${pathname}${searchParams}`
    const cleanedPath = pathname.endsWith('/') ? pathname.slice(0, -1) : pathname
    const paymentCheck = pathname.includes('/in-progress')
    const isForgotPassword = pathname.includes('/forgotPassword')
    const isPublicPage = ['/games', '/blog', '/faq', '/about-us', '/contact-us'].some((path) => pathname.includes(path))
    const isMainLandingPage = pathname.includes('/online-social-casino-games')
    const isPrivate = privateRoutes.some((route) => pathname.startsWith(route))
    const isVipRoute = pathname.includes('/vip-player-interests')

    return {
      searchParams,
      currentPath,
      cleanedPath,
      paymentCheck,
      isForgotPassword,
      isPublicPage,
      isMainLandingPage,
      isPrivate,
      isVipRoute
    }
  }, [pathname, location.search, privateRoutes])

  const {
    searchParams,
    currentPath,
    cleanedPath,
    paymentCheck,
    isForgotPassword,
    isPublicPage,
    isMainLandingPage,
    isPrivate,
    isVipRoute
  } = pathInfo

  // Memoize route creation with authentication state as dependency
  const applicationRoutes = useMemo(() => {
    return routesConfig.map((route) => {
      // Create route directly here to avoid hook violations
      if (route.level) {
        return (
          <Route path={route.path} key={route.path} element={route.element} {...route.props}>
            {route.level.map((subRoute) => {
              if (subRoute.private) {
                if (!isUserAuthenticate || !isAuthenticate) {
                  return <Route path={subRoute.path} key={subRoute.path} element={<Navigate replace to='/' />} />
                }
                return <Route path={subRoute.path} key={subRoute.path} element={subRoute.element} {...subRoute.props} />
              }
              if (isUserAuthenticate && isAuthenticate) {
                return <Route path={subRoute.path} key={subRoute.path} element={<Navigate replace to='/' />} />
              }
              return <Route path={subRoute.path} key={subRoute.path} element={subRoute.element} {...subRoute.props} />
            })}
          </Route>
        )
      } else {
        // for private route
        if (route.private) {
          if (!isUserAuthenticate || !isAuthenticate) {
            return <Route path={route.path} key={route.path} element={<Navigate replace to='/' />} />
          }
          return <Route path={route.path} key={route.path} element={route.element} {...route.props} />
        }
        // for only when user not logged in
        if (route.onlyWithoutAuth && isUserAuthenticate && isAuthenticate) {
          return <Route path={route.path} key={route.path} element={<Navigate replace to='/' />} />
        }
        return <Route path={route.path} key={route.path} element={route.element} {...route.props} />
      }
    })
  }, [routesConfig, isAuthenticate])

  // ===== CUSTOM HOOKS AND API CALLS =====
  const dynamoKeySend = useDynamoKey({
    onSuccess: (res) => {
      if (res.data.success) {
        toast.success('Message Sent')
        deleteCookie('dynamoKey')
      }
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  // ===== MEMOIZED METHODS =====
  // Memoize logout functions to prevent recreation on every render
  const handleLogoutAndNavigate = useMemo(
    () =>
      (navigateTo = '/') => {
        portalClosePortal()
        if (accessCookie) {
          deleteAccessTokenCookies('accessToken')
        }
        userLogout()
        navigate(navigateTo)
      },
    [portalClosePortal, accessCookie, userLogout, navigate]
  )

  const handleLogoutWithToast = useMemo(
    () =>
      (navigateTo = '/') => {
        portalClosePortal()
        if (accessCookie) {
          deleteAccessTokenCookies('accessToken')
        }
        userLogout()
        navigate(navigateTo)
      },
    [portalClosePortal, accessCookie, userLogout, navigate]
  )

  // ===== EFFECTS =====
  // URL parameters effect
  useEffect(() => {
    if (Object.keys(paramsData).length > 0) {
      const paramsString = JSON.stringify(paramsData)
      document.cookie = `urlParams=${paramsString};`
    }
  }, [paramsData])

  // Dynamo key effect
  useEffect(() => {
    if (dynamoKey !== null) {
      setCookie('dynamoKey', dynamoKey)
      if (userDetails !== null && dynamoKey !== '') {
        dynamoKeySend.mutate({ d10x_link_id: dynamoKey })
      }
    }
  }, [dynamoKey, userDetails])

  // Referral key effect
  useEffect(() => {
    if (refferalKey !== null) {
      setreferralCode('referralcode', refferalKey)
      setReferralSocialCookie('referralcode', refferalKey, '.themoneyfactory.com')
      deleteCookie('affiliateCode')
      deleteCookie('affiliateId')
      deleteCookie('affiliatePromocode')
    }
  }, [refferalKey])

  // Main redirect and navigation effect
  useEffect(() => {
    if (redirectHandled) return
    setRedirectHandled(true)

    // Legacy path redirects mapping
    const legacyRedirects = {
      '/blogs': '/blog',
      '/games/all-games': '/games',
      '/games/table-games': '/games/casino-table-games',
      '/games/live-dealer': '/games/live-dealer-casino-games',
      '/games/hot-games': '/games',
      '/home': '/online-social-casino-games',
      '/game': '/games',
      '/games/slots': '/games/social-slot-games',
      '/games/featured': '/games/popular-casino-games'
    }

    // Handle legacy redirects
    if (legacyRedirects[location.pathname]) {
      navigate(legacyRedirects[location.pathname], { replace: true })
      return
    }

    // VIP and authentication logic
    const isVipApproved = Boolean(userIsVipApproved)
    const pathCookie = getCookie('path')
    const vipEmail = params.get('email')

    if (isVipRoute && vipEmail) {
      document.cookie = `vipRoute=${location.pathname}${location.search}; path=/;`
      if (userDetailsEmail && vipEmail !== userDetailsEmail) {
        handleLogoutWithToast('/')
        return
      }
    }

    if (isForgotPassword) {
      localStorage.setItem('forgotPassword', params.get('token'))
      return
    }

    if (isPublicPage) {
      handleLogoutWithToast(pathname)
      return
    }

    if (pathCookie) {
      if (isPrivate && localStorage.getItem('username')) {
        navigate(currentPath, { replace: true })
      } else if (paymentCheck) {
        navigate(currentPath, { replace: true })
      } else if (isMainLandingPage || isPrivate) {
        if (!isVipApproved) {
          navigate('/')
        } else {
          navigate(`/${searchParams}`)
        }
      } else {
        navigate(currentPath, { replace: true })
      }
      return
    }

    // No cookie present
    if (isMainLandingPage) {
      handleLogoutAndNavigate(
        pathname.endsWith('/') ? `${cleanedPath}${searchParams}` : `/online-social-casino-games${searchParams}`
      )
      return
    }

    // Public routes handling (redundant if already handled above, but kept for clarity)
    const publicRoutes = ['/games', '/blog', '/faq', '/about-us', '/contact-us']
    if (publicRoutes.some((route) => pathname.includes(route))) {
      handleLogoutAndNavigate(pathname)
      return
    }

    // Scroll to top for non-game/cms pages
    if (!pathname.includes('/game') && !pathname.includes('/cms')) {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }, [location.pathname, location.search, redirectHandled, navigate, isUserAuthenticate])

  // ===== RENDER =====
  return (
    <div>
      <Grid className='main-page'>
        <MemoizedHeaderLayout showHeader={showHeader} />
        <Grid className={`${classes.lobbyWrap} ${socketRibbonData?.isRibbon ? 'msg-header' : ''}`}>
          <MemoizedSideBarLayout showSidebarAndFooter={showSidebarAndFooter} />
          <Suspense fallback={<Loader />}>
            <Routes>{applicationRoutes}</Routes>
          </Suspense>
        </Grid>
        <MemoizedFooterLayout showSidebarAndFooter={showSidebarAndFooter} />
      </Grid>
    </div>
  )
}

// Memoized AppRouter to prevent unnecessary re-renders
const AppRouter = memo(() => {
  // Selective subscription: only subscribe to isAuthenticate property
  const isAuthenticate = useUserStore((state) => state.isAuthenticate)
  isUserAuthenticate = isAuthenticate
  return buildRouter(appRoutes)
})

export default AppRouter
