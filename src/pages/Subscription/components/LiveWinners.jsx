import React, { useEffect, useState } from 'react'
import { Box, Typography, Card, Stack, Grid } from '@mui/material'
import { makeStyles } from '@mui/styles'
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord'
import { CasinoQuery } from '../../../reactQuery'
import { useUserStore } from '../../../store/useUserSlice'
import { liveWinnerSocket } from '../../../utils/socket'

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(4, 2),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(2, 0.5)
    }
  },
  heading: {
    textAlign: 'center',
    marginBottom: theme.spacing(2)
  },
  liveText: {
    color: 'white',
    fontSize: '2rem !important',
    fontWeight: '700 !important',
    [theme.breakpoints.down('md')]: {
      fontSize: '1.5rem !important'
    }
  },
  subText: {
    color: 'gray',
    fontSize: '1rem !important',
    fontWeight: '700 !important'
  },
  liveCard: {
    padding: theme.spacing(2),
    borderRadius: 16,
    marginBottom: theme.spacing(2),
    background: 'linear-gradient(to right, rgba(16,185,129,0.2), rgba(5,150,105,0.2))',
    border: '2px solid rgba(16,185,129,0.5)',
    boxShadow: theme.shadows[10],
    [theme.breakpoints.down('md')]: {
      margin: '0 0 1rem 0 !important'
    }
  },
  cardHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(2)
  },
  winnerAmount: {
    color: '#AAD840',
    fontSize: '3rem !important',
    fontWeight: '700 !important'
  },
  recentBox: {
    backgroundColor: '#1e1e1e',
    border: '1px solid #374151',
    borderRadius: 12,
    padding: theme.spacing(2),
    marginBottom: theme.spacing(2),
    [theme.breakpoints.down('md')]: {
      padding: theme.spacing(1)
    }
  },
  recentItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(1),
    borderRadius: 10,
    backgroundColor: '#2d2d2d'
  },
  recentItemActive: {
    backgroundColor: 'rgba(34,197,94,0.1)',
    border: '1px solid rgba(34,197,94,0.3)'
  },
  statsCard: {
    backgroundColor: '#1e1e1e',
    border: '1px solid #374151',
    padding: theme.spacing(1),
    [theme.breakpoints.down('md')]: {
      margin: '0 !important'
    }
  }
}))

const LiveWinners = () => {
  const classes = useStyles()
  const currentWinner = 0

  const liveWinnerSocketConnection = useUserStore((state) => state.liveWinnerSocketConnection)
  const [liveWinnersData, setLiveWinnersData] = useState([])

  // eslint-disable-next-line
  const { data: liveWinners, refetch } = CasinoQuery.getLiveWinners({
    params: {},
    enabled: true,
    onSuccess: (data) => {
      console.log('###LIVE_WINNERS', data)
      setLiveWinnersData(data?.data?.data)
      // console.log('###LIVE_WINNERS', data?.data?.data)
    },
    onError: (error) => {
      console.error('error', error)
    }
  })

  const onGetLiveWinners = (winnerData) => {
    setLiveWinnersData((winnerList) => {
      const updatedWinners = [winnerData?.data, ...winnerList.slice(0, winnerList.length - 1)]
      return updatedWinners
    })
  }

  useEffect(() => {
    if (liveWinnerSocketConnection) {
      liveWinnerSocket.on('LIVE_GAME_WINNERS', (data) => {
        onGetLiveWinners(data)
      })
    }
    return () => {
      liveWinnerSocket.off('LIVE_GAME_WINNERS', () => {})
    }
  }, [liveWinnerSocketConnection])

  return (
    <Box className={classes.root}>
      <Box className={classes.heading}>
        <Stack direction='row' justifyContent='center' alignItems='center' spacing={1} mb={1}>
          <FiberManualRecordIcon color='success' fontSize='small' />
          <Typography variant='h5' className={classes.liveText}>
            🏆 LIVE WINNERS
          </Typography>
          <FiberManualRecordIcon color='success' fontSize='small' />
        </Stack>
        <Typography variant='body2' className={classes.subText}>
          Real players winning real money right now!
        </Typography>
      </Box>

      {/* Winner Card */}
      <Card className={classes.liveCard}>
        <Box className={classes.cardHeader}>
          <Stack direction='row' alignItems='center' spacing={1}>
            <FiberManualRecordIcon color='success' fontSize='small' />
            <Typography color='success.main' fontWeight='bold' variant='body2'>
              🎉 JUST WON
            </Typography>
          </Stack>
        </Box>

        <Box textAlign='center'>
          <Typography variant='h3' className={classes.winnerAmount} mb={1}>
            {liveWinnersData[currentWinner]?.winAmount.toLocaleString()}
            {liveWinnersData[currentWinner]?.isScActive ? ' SC' : ' GC'}
          </Typography>
          <Typography variant='h6' color='white'>
            {liveWinnersData[currentWinner]?.username}
          </Typography>
          <Typography variant='body2' color='gray' mb={1}>
            Playing <strong style={{ color: '#0ea5e9' }}>{liveWinnersData[currentWinner]?.providerName}</strong>
          </Typography>
        </Box>
      </Card>

      {/* Recent Winners */}
      <Box className={classes.recentBox}>
        <Box display='flex' justifyContent='space-between' alignItems='center' mb={2}>
          <Typography variant='body2' color='white' fontWeight='bold'>
            Recent Winners (Last Hour)
          </Typography>
          <Stack direction='row' spacing={1} alignItems='center'>
            <FiberManualRecordIcon color='success' fontSize='small' />
            <Typography variant='caption' color='success.main' fontWeight='bold'>
              LIVE
            </Typography>
          </Stack>
        </Box>

        <Stack spacing={1}>
          {liveWinnersData?.map((winner, i) => (
            <Box key={i} className={`${classes.recentItem} ${i === currentWinner ? classes.recentItemActive : ''}`}>
              <Stack direction='row' spacing={1} alignItems='center'>
                <FiberManualRecordIcon fontSize='small' sx={{ color: i === currentWinner ? 'success.main' : 'gray' }} />
                <Typography color='white' variant='body2'>
                  {winner.username}
                </Typography>
                <Typography variant='caption' color='gray'>
                  • {winner.providerName}
                </Typography>
              </Stack>
              <Stack direction='row' spacing={1} alignItems='center'>
                <Typography variant='body2' fontWeight='bold' color='success.main'>
                  {winner.winAmount.toLocaleString()}
                  {winner.isScActive ? ' SC' : ' GC'}
                </Typography>
              </Stack>
            </Box>
          ))}
        </Stack>
      </Box>

      {/* Stats */}
      <Grid container spacing={{ xs: 1, sm: 2 }}>
        <Grid item xs={12} md={4}>
          <Card className={classes.statsCard}>
            <Typography color='success.main' fontWeight='bold' variant='h6'>
              $47K+
            </Typography>
            <Typography variant='caption' color='gray'>
              Won Today
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card className={classes.statsCard}>
            <Typography color='primary' fontWeight='bold' variant='h6'>
              156
            </Typography>
            <Typography variant='caption' color='gray'>
              Winners Today
            </Typography>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card className={classes.statsCard}>
            <Typography sx={{ color: '#60a5fa' }} fontWeight='bold' variant='h6'>
              $2.1M
            </Typography>
            <Typography variant='caption' color='gray'>
              This Month
            </Typography>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default LiveWinners
