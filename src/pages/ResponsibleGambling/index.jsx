import { <PERSON><PERSON>, <PERSON>rid, <PERSON><PERSON>ield, Typography, InputAdornment, IconButton, Tooltip, Box } from '@mui/material'
import React, { useEffect, useState } from 'react'
import useStyles from './Responsible.styles'
import { ResponsibleQuery, useLogOutMutation } from '../../reactQuery'
import { toast } from 'react-hot-toast'
import { useUserStore } from '../../store/useUserSlice'
import Delete from '@mui/icons-material/Delete'
import AssignmentTurnedInIcon from '@mui/icons-material/AssignmentTurnedIn'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import moment from 'moment'
import { commonDateTimeFormat } from '../../utils/helpers'
import useLogout from '../../hooks/useLogout'
import { isNull, isUndefined } from '../../utils/lodashLite'

const ResponsibleGambling = () => {
  const NumberOflimit = 20
  const classes = useStyles()
  const [data, setData] = useState([])
  const user = useUserStore((state) => state)
  const [dailyBetLimit, setDailyBetLimit] = useState('')
  const [weeklyBetLimit, setWeeklyBetLimit] = useState('')
  const [monthlyBetLimit, setMonthlyBetLimit] = useState('')

  const [dailyDepositLimit, setDailyDepositLimit] = useState('')
  const [weeklyDepositLimit, setWeeklyDepositLimit] = useState('')
  const [monthlyDepositLimit, setMonthlyDepositLimit] = useState('')
  const [timeBreak, setTimeBreak] = useState('')

  const [open, setOpen] = useState(false)
  const [removingLimit, setRemovingLimit] = useState('')
  const [selfExcusionOpen, setSelfExcusionOpen] = useState(false)
  const totalBetsLimits = user?.userDetails?.totalBetLimits
  const totalDepositLimits = user?.userDetails?.totalDepositLimits
  const [isOpenTotalLimit, setIsOpenTotalLimit] = useState(false)
  const [limitMessage, setLimitMessage] = useState('')
  const [updatingName, setUpdatingName] = useState('')
  const { logoutHandler } = useLogout()
  const updateMutation = ResponsibleQuery.useUpdateResponsibleMutation({
    onSuccess: (res) => {
      if (res?.data?.isUpdated) {
        if (res?.data?.timebreak || res?.data?.selfExcluded) {
          logoutMutation.mutate()
          user.logout()
        }
        toast.success(res?.data?.message)
      }
      setUpdatingName('')
      setIsOpenTotalLimit(false)
      getMutation?.mutate()
    },
    onError: () => {
      setUpdatingName('')
      setIsOpenTotalLimit(false)
    }
  })

  const getMutation = ResponsibleQuery.useGetGamblingData({
    onSuccess: (res) => {
      setData(res?.data?.responsibleGambling)
    },
    onError: () => {}
  })

  const logoutMutation = useLogOutMutation({
    onSuccess: (res) => {
      logoutHandler()
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })

  const removeMutation = ResponsibleQuery.useRemoveResponsibleLimitMutation({
    onSuccess: (res) => {
      if (res?.data?.message) {
        if (removingLimit === 'daily purchase limit') {
          setDailyDepositLimit('')
        }

        if (removingLimit === 'weekly purchase limit') {
          setWeeklyDepositLimit('')
        }

        if (removingLimit === 'monthly purchase limit') {
          setMonthlyDepositLimit('')
        }

        if (removingLimit === 'daily play limit') {
          setDailyBetLimit('')
        }

        if (removingLimit === 'weekly play limit') {
          setWeeklyBetLimit('')
        }

        if (removingLimit === 'monthly play limit') {
          setMonthlyBetLimit('')
        }
        toast.success(res?.data?.message)
        setRemovingLimit('')
      }
      getMutation.mutate()
    },
    onError: () => {
      setRemovingLimit('')
    }
  })

  const removeLimit = () => {
    if (removingLimit !== '') {
      if (removingLimit === 'daily purchase limit') {
        removeMutation.mutate({ limitType: '1', responsibleGamblingType: '2' })
      }

      if (removingLimit === 'weekly purchase limit') {
        removeMutation.mutate({ limitType: '2', responsibleGamblingType: '2' })
      }

      if (removingLimit === 'monthly purchase limit') {
        removeMutation.mutate({ limitType: '3', responsibleGamblingType: '2' })
      }

      if (removingLimit === 'daily play limit') {
        removeMutation.mutate({ limitType: '1', responsibleGamblingType: '1' })
      }

      if (removingLimit === 'weekly play limit') {
        removeMutation.mutate({ limitType: '2', responsibleGamblingType: '1' })
      }

      if (removingLimit === 'monthly play limit') {
        removeMutation.mutate({ limitType: '3', responsibleGamblingType: '1' })
      }
    }
    setOpen(false)
  }

  const onSubmitLimit = (name) => {
    if (name === 'timeBreak') {
      if (
        !(timeBreak === '' || timeBreak === '0' || timeBreak.toString().includes('e') || Number(timeBreak) < 0) &&
        Number(timeBreak) % 1 === 0
      ) {
        updateMutation.mutate({ timeBreak })
      } else {
        toast.error('Time break should be whole number and greater than 0')
      }
    }

    if (name === 'dailyDepositLimit') {
      const createdAt = data?.dailyDepositLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not update daily purchase limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }

      if (
        !(
          dailyDepositLimit === '' ||
          dailyDepositLimit === '0' ||
          dailyDepositLimit.toString().includes('e') ||
          Number(dailyDepositLimit) < 0
        )
      ) {
        if (
          isNull(totalDepositLimits?.totalDepositAmountToday) ||
          isUndefined(totalDepositLimits?.totalDepositAmountToday)
        ) {
          updateMutation.mutate({ dailyDepositLimit })
        } else {
          setLimitMessage(
            `You have spent total daily purchase limit $${
              totalDepositLimits?.totalDepositAmountToday % 1 != 0
                ? (totalDepositLimits?.totalDepositAmountToday).toFixed(2)
                : totalDepositLimits?.totalDepositAmountToday
            }, Are you sure you want to continue?`
          )
          setIsOpenTotalLimit(true)
          setUpdatingName('dailyDepositLimit')
        }
      } else {
        toast.error('Please enter valid value for daily purchase limit')
      }
    }

    if (name === 'weeklyDepositLimit') {
      const createdAt = data?.weeklyDepositLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not update weekly purchase limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }

      if (
        !(
          weeklyDepositLimit === '' ||
          weeklyDepositLimit === '0' ||
          weeklyDepositLimit.toString().includes('e') ||
          Number(weeklyDepositLimit) < 0
        )
      ) {
        if (
          isNull(totalDepositLimits?.totalDepositAmountWeekly) ||
          isUndefined(totalDepositLimits?.totalDepositAmountWeekly)
        ) {
          updateMutation.mutate({ weeklyDepositLimit })
        } else {
          setLimitMessage(
            `You have spent total weekly purchase limit $${
              totalDepositLimits?.totalDepositAmountWeekly % 1 != 0
                ? (totalDepositLimits?.totalDepositAmountWeekly).toFixed(2)
                : totalDepositLimits?.totalDepositAmountWeekly
            }, Are you sure you want to continue?`
          )
          setIsOpenTotalLimit(true)
          setUpdatingName('weeklyDepositLimit')
        }
      } else {
        toast.error('Please enter valid value for weekly purchase limit')
      }
    }

    if (name === 'monthlyDepositLimit') {
      const createdAt = data?.monthlyDepositLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not update monthly purchase limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }

      if (
        !(
          monthlyDepositLimit === '' ||
          monthlyDepositLimit === '0' ||
          monthlyDepositLimit.toString().includes('e') ||
          Number(monthlyDepositLimit) < 0
        )
      ) {
        if (
          isNull(totalDepositLimits?.totalDepositAmountMonthly) ||
          isUndefined(totalDepositLimits?.totalDepositAmountMonthly)
        ) {
          updateMutation.mutate({ monthlyDepositLimit })
        } else {
          setLimitMessage(
            `You have spent total monthly purchase limit $${
              totalDepositLimits?.totalDepositAmountMonthly % 1 != 0
                ? (totalDepositLimits?.totalDepositAmountMonthly).toFixed(2)
                : totalDepositLimits?.totalDepositAmountMonthly
            }, Are you sure you want to continue?`
          )
          setIsOpenTotalLimit(true)
          setUpdatingName('monthlyDepositLimit')
        }
      } else {
        toast.error('Please enter valid value for monthly purchase limit')
      }
    }

    if (name === 'dailyBetLimit') {
      const createdAt = data?.dailyBetLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not update daily plays limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }

      if (
        !(
          dailyBetLimit === '' ||
          dailyBetLimit === '0' ||
          dailyBetLimit.toString().includes('e') ||
          Number(dailyBetLimit) < 0
        )
      ) {
        if (isNull(totalBetsLimits?.totalBetAmountToday) || isUndefined(totalBetsLimits?.totalBetAmountToday)) {
          updateMutation.mutate({ dailyBetLimit })
        } else {
          setLimitMessage(
            `You have spent total daily plays limit ${
              totalBetsLimits?.totalBetAmountToday % 1 != 0
                ? (totalBetsLimits?.totalBetAmountToday).toFixed(2)
                : totalBetsLimits?.totalBetAmountToday
            } SC, Are you sure you want to continue?`
          )
          setIsOpenTotalLimit(true)
          setUpdatingName('dailyBetLimit')
        }
      } else {
        toast.error('Please enter valid value for daily plays limit')
      }
    }

    if (name === 'weeklyBetLimit') {
      const createdAt = data?.weeklyBetLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not update weekly plays limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }

      if (
        !(
          weeklyBetLimit === '' ||
          weeklyBetLimit === '0' ||
          weeklyBetLimit.toString().includes('e') ||
          Number(weeklyBetLimit) < 0
        )
      ) {
        if (isNull(totalBetsLimits?.totalBetAmountWeekly) || isUndefined(totalBetsLimits?.totalBetAmountWeekly)) {
          updateMutation.mutate({ weeklyBetLimit })
        } else {
          setLimitMessage(
            `You have spent total weekly plays limit ${
              totalBetsLimits?.totalBetAmountWeekly % 1 != 0
                ? (totalBetsLimits?.totalBetAmountWeekly).toFixed(2)
                : totalBetsLimits?.totalBetAmountWeekly
            } SC, Are you sure you want to continue?`
          )
          setIsOpenTotalLimit(true)
          setUpdatingName('weeklyBetLimit')
        }
      } else {
        toast.error('Please enter valid value for weekly plays limit')
      }
    }

    if (name === 'monthlyBetLimit') {
      const createdAt = data?.monthlyBetLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not update monthly plays limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }

      if (
        !(
          monthlyBetLimit === '' ||
          monthlyBetLimit === '0' ||
          monthlyBetLimit.toString().includes('e') ||
          Number(monthlyBetLimit) < 0
        )
      ) {
        if (isNull(totalBetsLimits?.totalBetAmountMonthly) || isUndefined(totalBetsLimits?.totalBetAmountMonthly)) {
          updateMutation.mutate({ monthlyBetLimit })
        } else {
          setLimitMessage(
            `You have spent total monthly plays limit ${
              totalBetsLimits?.totalBetAmountMonthly % 1 != 0
                ? (totalBetsLimits?.totalBetAmountMonthly).toFixed(2)
                : totalBetsLimits?.totalBetAmountMonthly
            } SC, Are you sure you want to continue?`
          )
          setIsOpenTotalLimit(true)
          setUpdatingName('monthlyBetLimit')
        }
      } else {
        toast.error('Please enter valid value for monthly plays limit')
      }
    }
  }

  useEffect(() => getMutation?.mutate(), [])
  useEffect(() => {
    if (data) {
      data?.dailyDepositLimit?.amount
        ? setDailyDepositLimit(String(data?.dailyDepositLimit?.amount))
        : setDailyDepositLimit('')
      data?.weeklyDepositLimit?.amount
        ? setWeeklyDepositLimit(String(data?.weeklyDepositLimit?.amount))
        : setWeeklyDepositLimit('')
      data?.monthlyDepositLimit?.amount
        ? setMonthlyDepositLimit(String(data?.monthlyDepositLimit?.amount))
        : setMonthlyDepositLimit('')
      data?.dailyBetLimit?.amount ? setDailyBetLimit(String(data?.dailyBetLimit?.amount)) : setDailyBetLimit('')
      data?.weeklyBetLimit?.amount ? setWeeklyBetLimit(String(data?.weeklyBetLimit?.amount)) : setWeeklyBetLimit('')
      data?.monthlyBetLimit?.amount ? setMonthlyBetLimit(String(data?.monthlyBetLimit?.amount)) : setMonthlyBetLimit('')
    }
  }, [data])

  const handleClickOpen = (name) => {
    if (name === 'daily purchase limit' && dailyDepositLimit !== '') {
      const createdAt = data?.dailyDepositLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not remove daily purchase limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }
      setOpen(true)
      setRemovingLimit(name)
    }

    if (name === 'weekly purchase limit' && weeklyDepositLimit !== '') {
      const createdAt = data?.weeklyDepositLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not remove weekly purchase limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }
      setOpen(true)
      setRemovingLimit(name)
    }

    if (name === 'monthly purchase limit' && monthlyDepositLimit !== '') {
      const createdAt = data?.monthlyDepositLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not remove monthly purchase limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }
      setOpen(true)
      setRemovingLimit(name)
    }

    if (name === 'daily play limit' && dailyBetLimit !== '') {
      const createdAt = data?.dailyBetLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not remove daily plays limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }
      setOpen(true)
      setRemovingLimit(name)
    }

    if (name === 'weekly play limit' && weeklyBetLimit !== '') {
      const createdAt = data?.weeklyBetLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not remove weekly plays limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }
      setOpen(true)
      setRemovingLimit(name)
    }

    if (name === 'monthly play limit' && monthlyBetLimit !== '') {
      const createdAt = data?.monthlyBetLimit?.createdAt
      if (!isUndefined(createdAt)) {
        const timeDifference = (new Date() - new Date(createdAt)) / (60 * 60 * 1000)
        if (timeDifference < 24) {
          const date = new Date(createdAt)
          return toast.error(
            `You can not remove monthly plays limit till ${moment(date)
              .add(1, 'days')
              .format(commonDateTimeFormat.dateWithTime)}`
          )
        }
      }
      setOpen(true)
      setRemovingLimit(name)
    }
  }

  const handleClose = () => {
    setOpen(false)
    setRemovingLimit('')
  }

  const setSelfExclusionLimit = () => {
    setRemovingLimit('')
    updateMutation.mutate({ selfExclusion: 'yes' })
  }

  const handleCloseSelfExclusion = () => {
    setSelfExcusionOpen(false)
    setRemovingLimit('')
  }

  const handleOpenSelfExclusion = (e) => {
    if (e === 'yes') {
      setSelfExcusionOpen(true)
    }
  }

  const handleCloseLimitModal = (e) => {
    setIsOpenTotalLimit(false)
  }

  const handleSetLimit = (e) => {
    if (updatingName !== '') {
      if (updatingName === 'dailyDepositLimit') {
        updateMutation.mutate({ dailyDepositLimit })
      }

      if (updatingName === 'weeklyDepositLimit') {
        updateMutation.mutate({ weeklyDepositLimit })
      }

      if (updatingName === 'monthlyDepositLimit') {
        updateMutation.mutate({ monthlyDepositLimit })
      }

      if (updatingName === 'dailyBetLimit') {
        updateMutation.mutate({ dailyBetLimit })
      }

      if (updatingName === 'weeklyBetLimit') {
        updateMutation.mutate({ weeklyBetLimit })
      }

      if (updatingName === 'monthlyBetLimit') {
        updateMutation.mutate({ monthlyBetLimit })
      }
    }
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        className={classes.innerModal}
        aria-labelledby='alert-dialog-title'
        aria-describedby='alert-dialog-description'
      >
        <Grid className='inner-modal-header'>
          <Typography variant='h4' style={{ color: '#fff' }}>
            Remove Limit?
          </Typography>
        </Grid>
        <DialogContent>
          <Grid className='kyc-content'>
            <Typography variant='h4' style={{ color: '#fff' }}>
              Are you sure you want to remove {removingLimit} ?
            </Typography>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Grid className='btn-wrap'>
            <Grid className={classes.btnWhiteGradient}>
              <Button
                variant='contained'
                className='btn-gradient'
                onClick={handleClose}
                style={{ background: '#FDB72E' }}
              >
                <span className='btn-span'>No</span>
              </Button>
            </Grid>
            <Grid className={classes.btnGradientWrap}>
              <Button
                variant='contained'
                className='btn-gradient'
                type='submit'
                onClick={removeLimit}
                autoFocus
                style={{ background: '#FDB72E' }}
              >
                Yes
              </Button>
            </Grid>
          </Grid>
        </DialogActions>
      </Dialog>
      <Dialog
        open={selfExcusionOpen}
        onClose={handleCloseSelfExclusion}
        className={classes.innerModal}
        aria-labelledby='alert-dialog-title'
        aria-describedby='alert-dialog-description'
      >
        <Grid className='inner-modal-header'>
          <Typography variant='h4' style={{ color: '#fff' }}>
            Self Exclusion?
          </Typography>
        </Grid>
        <DialogContent>
          <Grid className='kyc-content'>
            <Typography variant='h4' style={{ color: '#fff' }}>
              Are you sure you want to self exclude yourself?
            </Typography>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Grid className='btn-wrap'>
            <Grid className={classes.btnWhiteGradient}>
              <Button
                variant='contained'
                className='btn-gradient'
                onClick={handleCloseSelfExclusion}
                style={{ background: '#FDB72E' }}
              >
                <span className='btn-span'>No</span>
              </Button>
            </Grid>
            <Grid className={classes.btnGradientWrap}>
              <Button
                variant='contained'
                className='btn-gradient'
                type='submit'
                onClick={setSelfExclusionLimit}
                autoFocus
                style={{ background: '#FDB72E' }}
              >
                Yes
              </Button>
            </Grid>
          </Grid>
        </DialogActions>
      </Dialog>
      <Dialog
        open={isOpenTotalLimit}
        onClose={handleCloseLimitModal}
        className={classes.innerModal}
        aria-labelledby='alert-dialog-title'
        aria-describedby='alert-dialog-description'
      >
        <Grid className='inner-modal-header'>
          <Typography variant='h4' style={{ color: '#fff' }}>
            Info
          </Typography>
        </Grid>
        <DialogContent>
          <Grid className='kyc-content'>
            <Typography variant='h4' style={{ color: '#fff' }}>
              {limitMessage}
            </Typography>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Grid className='btn-wrap'>
            <Grid className={classes.btnWhiteGradient}>
              <Button
                variant='contained'
                className='btn-gradient'
                onClick={handleCloseLimitModal}
                style={{ background: '#FDB72E' }}
              >
                <span className='btn-span'>No</span>
              </Button>
            </Grid>
            <Grid className={classes.btnGradientWrap}>
              <Button
                variant='contained'
                className='btn-gradient'
                type='submit'
                onClick={handleSetLimit}
                autoFocus
                style={{ background: '#FDB72E' }}
              >
                Yes
              </Button>
            </Grid>
          </Grid>
        </DialogActions>
      </Dialog>
      <Grid className='gridHeading'>
        <Typography variant='h3'>Purchase Limit ($)</Typography>
        <Box className='profile'>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4} display='flex' className={classes.gamblingInput}>
              <TextField
                id='outlined-basic'
                label=''
                variant='outlined'
                placeholder='Daily Limit'
                type='number'
                onKeyDown={(evt) => ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()}
                min='1'
                value={dailyDepositLimit}
                onChange={(e) => !(e?.target?.value?.length > NumberOflimit) && setDailyDepositLimit(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position='end'>
                      <Tooltip title='Set daily purchase limit'>
                        <IconButton
                          aria-label='save daily limit'
                          edge='end'
                          onClick={() => onSubmitLimit('dailyDepositLimit')}
                        >
                          <AssignmentTurnedInIcon />
                        </IconButton>
                      </Tooltip>
                      {data?.dailyDepositLimit?.amount && (
                        <Tooltip title='Remove daily purchase limit'>
                          <IconButton
                            aria-label='remove daily limit'
                            onClick={() => handleClickOpen('daily purchase limit')}
                            edge='end'
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      )}
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} sm={4} display='flex' className={classes.gamblingInput}>
              <TextField
                id='outlined-basic'
                label=''
                variant='outlined'
                placeholder='Weekly Limit'
                type='number'
                onKeyDown={(evt) => ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()}
                value={weeklyDepositLimit}
                onChange={(e) => !(e?.target?.value?.length > NumberOflimit) && setWeeklyDepositLimit(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position='end'>
                      <Tooltip title='Set weekly purchase limit'>
                        <IconButton
                          aria-label='save weekly limit'
                          edge='end'
                          onClick={() => onSubmitLimit('weeklyDepositLimit')}
                        >
                          <AssignmentTurnedInIcon />
                        </IconButton>
                      </Tooltip>
                      {data?.weeklyDepositLimit?.amount && (
                        <Tooltip title='Remove weekly purchase limit'>
                          <IconButton
                            aria-label='remove weekly limit'
                            onClick={() => handleClickOpen('weekly purchase limit')}
                            edge='end'
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      )}
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} sm={4} display='flex' className={classes.gamblingInput}>
              <TextField
                id='outlined-basic'
                label=''
                variant='outlined'
                placeholder='Monthly Limit'
                type='number'
                value={monthlyDepositLimit}
                onKeyDown={(evt) => ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()}
                onChange={(e) => !(e?.target?.value?.length > NumberOflimit) && setMonthlyDepositLimit(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position='end'>
                      <Tooltip title='Set monthly purchase limit'>
                        <IconButton
                          aria-label='save monthly limit'
                          edge='end'
                          onClick={() => onSubmitLimit('monthlyDepositLimit')}
                        >
                          <AssignmentTurnedInIcon />
                        </IconButton>
                      </Tooltip>
                      {data?.monthlyDepositLimit?.amount && (
                        <Tooltip title='Remove monthly purchase limit'>
                          <IconButton
                            aria-label='remove monthly limit'
                            onClick={() => handleClickOpen('monthly purchase limit')}
                            edge='end'
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      )}
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
          </Grid>
        </Box>
      </Grid>

      <Grid className='gridHeading'>
        <Typography variant='h3'>Risk Limit (SC)</Typography>
        <Box className='profile'>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4} display='flex' className={classes.gamblingInput}>
              <TextField
                id='outlined-basic'
                label=''
                variant='outlined'
                placeholder='Daily Limit'
                type='number'
                onKeyDown={(evt) => ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()}
                value={dailyBetLimit}
                onChange={(e) => !(e?.target?.value?.length > NumberOflimit) && setDailyBetLimit(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position='end'>
                      <Tooltip title='Set daily Play limit'>
                        <IconButton
                          aria-label='save daily limit'
                          edge='end'
                          onClick={() => onSubmitLimit('dailyBetLimit')}
                        >
                          <AssignmentTurnedInIcon />
                        </IconButton>
                      </Tooltip>
                      {data?.dailyBetLimit?.amount && (
                        <Tooltip title='Remove daily Play limit'>
                          <IconButton
                            aria-label='remove daily limit'
                            onClick={() => handleClickOpen('daily play limit')}
                            edge='end'
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      )}
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} sm={4} display='flex' className={classes.gamblingInput}>
              <TextField
                id='outlined-basic'
                label=''
                variant='outlined'
                placeholder='Weekly Limit'
                type='number'
                onKeyDown={(evt) => ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()}
                value={weeklyBetLimit}
                onChange={(e) => !(e?.target?.value?.length > NumberOflimit) && setWeeklyBetLimit(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position='end'>
                      <Tooltip title='Set weekly Play limit'>
                        <IconButton
                          aria-label='save weekly limit'
                          edge='end'
                          onClick={() => onSubmitLimit('weeklyBetLimit')}
                        >
                          <AssignmentTurnedInIcon />
                        </IconButton>
                      </Tooltip>
                      {data?.weeklyBetLimit?.amount && (
                        <Tooltip title='Remove weekly Play limit'>
                          <IconButton
                            aria-label='remove weekly limit'
                            onClick={() => handleClickOpen('weekly play limit')}
                            edge='end'
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      )}
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} sm={4} display='flex' className={classes.gamblingInput}>
              <TextField
                id='outlined-basic'
                label=''
                variant='outlined'
                placeholder='Monthly Limit'
                type='number'
                onKeyDown={(evt) => ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()}
                value={monthlyBetLimit}
                onChange={(e) => !(e?.target?.value?.length > NumberOflimit) && setMonthlyBetLimit(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position='end'>
                      <Tooltip title='Set monthly Play limit'>
                        <IconButton
                          aria-label='save monthly limit'
                          edge='end'
                          onClick={() => onSubmitLimit('monthlyBetLimit')}
                        >
                          <AssignmentTurnedInIcon />
                        </IconButton>
                      </Tooltip>
                      {data?.monthlyBetLimit?.amount && (
                        <Tooltip title='Remove monthly Play limit'>
                          <IconButton
                            aria-label='remove monthly limit'
                            onClick={() => handleClickOpen('monthly play limit')}
                            edge='end'
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      )}
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
          </Grid>
        </Box>
      </Grid>

      <Grid className='gridHeading'>
        <Typography variant='h3'>Self Exclusion</Typography>
        <Box className='profile'>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4} display='flex' className={classes.gamblingSelect}>
              <select
                labelId='demo-simple-select-label'
                id='demo-simple-select'
                className='inputSelect'
                placeholder='Select value'
                label=''
                value=''
                onChange={(e) => handleOpenSelfExclusion(e.target.value)}
              >
                <option value='' defaultValue>
                  ---Select---
                </option>
                <option value={'yes'}>Yes</option>
              </select>
            </Grid>
          </Grid>
        </Box>
      </Grid>

      <Grid className='gridHeading'>
        <Typography variant='h3'>Time Break (in Days)</Typography>

        <Box className='profile'>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4} display='flex' className={classes.gamblingInput}>
              <TextField
                id='outlined-basic'
                label=''
                variant='outlined'
                placeholder='Time Break'
                type='number'
                value={timeBreak}
                onKeyDown={(evt) => ['e', 'E', '+', '-', '.'].includes(evt.key) && evt.preventDefault()}
                onChange={(e) => {
                  if (e?.target?.value?.length > 3) {
                    e.preventDefault()
                  } else {
                    setTimeBreak(e.target.value)
                  }
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position='end'>
                      <Tooltip title='Set time break'>
                        <IconButton aria-label='save time break' edge='end' onClick={() => onSubmitLimit('timeBreak')}>
                          <AssignmentTurnedInIcon />
                        </IconButton>
                      </Tooltip>
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
          </Grid>
        </Box>
      </Grid>
    </>
  )
}

export default ResponsibleGambling
