import { makeStyles } from '@mui/styles'

import { otpBg } from '../../components/ui-kit/icons/webp'

export default makeStyles((theme) => ({
  lobbyRight: {
    [theme.breakpoints.down('lg')]: {
      width: '100%',
      marginLeft: '0',
      //   marginTop: theme.spacing(8),
      //   padding: theme.spacing(0.313, 1),
      minHeight: 'auto'
    },
    [theme.breakpoints.down('md')]: {
      paddingTop: theme.spacing(0),
      marginTop: theme.spacing(1)
    },
    minHeight: 'auto',

    '& .package-page': {
      maxWidth: theme.spacing(74.6875),
      margin: '0 auto',
      maxHeight: '74dvh',
      padding: '1.25rem 1rem',
      border: '1px solid #404144',
      borderRadius: '0.5rem',
      overflow: 'auto',
      [theme.breakpoints.down('md')]: {
        maxHeight: '67dvh',
        padding: '1rem 0.75rem'
      },
      [theme.breakpoints.down('sm')]: {
        maxHeight: '65dvh'
      },

      '& .free-tag': {
        width: theme.spacing(3),
        [theme.breakpoints.down('sm')]: {
          width: theme.spacing(2)
        }
      },
      '& .btn-primary': {
        color: theme.colors.textBlack,
        [theme.breakpoints.down('md')]: {
          maxWidth: theme.spacing(12.25),
          marginLeft: 'auto'
        },
        '&:hover': {
          color: theme.colors.YellowishOrange
        }
      },

      '& .special-offer-section': {
        marginBottom: theme.spacing(0.5),
        [theme.breakpoints.down('lg')]: {
          margin: theme.spacing(1, 0, 0.5)
        },

        '& .offer-graphics': {
          width: '50%',
          [theme.breakpoints.down('sm')]: {
            width: '70%'
          }
        },
        '& .package-details': {
          background: theme.colors.packageDetailsBg,
          border: `1px solid ${theme.colors.packageInnerCard}`,
          padding: theme.spacing(1, 0, 0.5),
          borderRadius: theme.spacing(1.875),
          display: 'flex',
          flexDirection: 'column',
          //   minHeight: theme.spacing(10.75),
          alignItems: 'center',
          [theme.breakpoints.down('md')]: {
            borderRadius: theme.spacing(0.9375),
            padding: theme.spacing(0.5, 0.313, 0),
            minHeight: 'auto'
          },
          '& .package-content, & .package-price': {
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(0.625),
            fontWeight: '500',
            [theme.breakpoints.down('sm')]: {
              gap: theme.spacing(0.3)
            }
          },
          '& .package-price': {
            color: '#FFFFFF',
            [theme.breakpoints.down('md')]: {
              //   fontSize: theme.spacing(0.875)
            },
            [theme.breakpoints.down('sm')]: {
              //   fontSize: theme.spacing(0.75)
            }
          },
          '& .MuiButtonBase-root': {
            marginTop: theme.spacing(0.5),
            fontSize: theme.spacing(1.18),
            minWidth: theme.spacing(11.888),
            minHeight: theme.spacing(0.125),
            padding: '0.125rem',
            fontWeight: '600',
            [theme.breakpoints.down('md')]: {
              minWidth: theme.spacing(5.944),
              minHeight: theme.spacing(2.125),
              fontSize: theme.spacing(0.875),
              lineHeight: theme.spacing(1),
              margin: '0.5rem auto 0.625rem'
            }
          }
        },
        '& .MuiGrid-container': {
          alignItems: 'center'
        },
        '& .badge-grid': {
          position: 'absolute',
          top: theme.spacing(-1.875),
          right: theme.spacing(0.625),
          [theme.breakpoints.down(1199)]: {
            top: theme.spacing(-2.5)
          },
          [theme.breakpoints.down(983.99)]: {
            top: theme.spacing(-2.5),
            order: 2,
            right: theme.spacing(0.625)
          },
          [theme.breakpoints.down(899.99)]: {
            top: theme.spacing(-2.3125),
            order: 2,
            right: theme.spacing(0.625)
          },
          [theme.breakpoints.down(600)]: {
            top: theme.spacing(-1.875)
          },
          '& .offer-badge': {
            position: 'relative',
            top: '11px',
            zIndex: 2,
            [theme.breakpoints.down('lg')]: {
              top: '4px'
            },
            [theme.breakpoints.down(1400)]: {
              top: '8px'
            },
            [theme.breakpoints.down('md')]: {
              top: '7px'
            },
            [theme.breakpoints.down('sm')]: {
              top: '2px'
            },
            '& .sp-offer-badge-content': {
              position: 'absolute',
              top: '39%',
              left: '50%',
              transform: 'translate(-50%,-50%)',
              marginInline: 'auto',
              width: 'fit-content',
              color: '#FFFFFF',
              '& h5': {
                fontWeight: 'bold',
                fontSize: theme.spacing(1),
                lineHeight: theme.spacing(2),

                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.875),
                  lineHeight: theme.spacing(0.875)
                }
              },
              '& p': {
                fontWeight: 'bold',
                fontSize: theme.spacing(1),
                textAlign: 'center',

                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.75),
                  lineHeight: theme.spacing(0.8)
                }
              }
            },
            [theme.breakpoints.down('sm')]: {
              '& .sp-offer-badge-content': {
                top: '3px',
                left: '50%',
                position: 'absolute',
                marginInline: 'auto',
                width: 'fit-content'
              }
            },

            '& img': {
              height: theme.spacing(7),

              [theme.breakpoints.down('md')]: {
                height: theme.spacing(7)
              },
              [theme.breakpoints.down('sm')]: {
                height: theme.spacing(5.25)
              }
            }
          }
        },

        '& .package-details-grid': {
          [theme.breakpoints.down('md')]: {
            order: 3
          }
        }
      },
      '& .package-card': {
        background: theme.colors.packageDetailsBg,
        border: `1px solid ${theme.colors.packageInnerCard}`,
        padding: theme.spacing(0.5, 1.25),
        borderRadius: theme.spacing(2.5),
        // minHeight: theme.spacing(13.4375),
        position: 'relative',
        display: 'flex',
        [theme.breakpoints.down('lg')]: {
          padding: theme.spacing(1)
        },
        [theme.breakpoints.down('md')]: {
          borderRadius: theme.spacing(0.625),
          padding: theme.spacing(0.9375)
        }
      },
      '& .purchase-section': {
        '& .purchase-section-content': {
          //   background: theme.colors.coinBundle,
          //   border: `1px solid ${theme.colors.packageInnerCard}`,
          //   padding: theme.spacing(2.5, 2.0625),
          borderRadius: theme.spacing(2.5),
          [theme.breakpoints.down('lg')]: {
            borderRadius: theme.spacing(1)
          },

          '& .package-card': {
            background: '#20242C',
            border: '1px solid #4D535F',
            minHeight: theme.spacing(4.583),

            marginBottom: theme.spacing(0.5),
            borderRadius: theme.spacing(1.5),

            [theme.breakpoints.down('lg')]: {
              marginBottom: theme.spacing(0.5),
              borderRadius: theme.spacing(0.5625)
            },
            '&:last-child': {
              marginBottom: '0'
            },
            '& .MuiGrid-container': {
              alignItems: 'center',
              '& .bonus-badge': {
                position: 'relative',
                left: theme.spacing(-2.275),
                width: theme.spacing(4.444),
                [theme.breakpoints.down(1500)]: {
                  left: theme.spacing(-2.312)
                  // width: theme.spacing(5.625),
                },
                [theme.breakpoints.down(1400)]: {
                  width: theme.spacing(5.1)
                },
                [theme.breakpoints.down('lg')]: {
                  width: theme.spacing(7),
                  left: theme.spacing(-2.75)
                },
                [theme.breakpoints.down('md')]: {
                  left: theme.spacing(-2.625)
                },
                [theme.breakpoints.down('sm')]: {
                  width: theme.spacing(4.5),
                  left: theme.spacing(-2.125)
                }
              },
              '& .bonus-graphics': {
                width: theme.spacing(4.722),
                height: theme.spacing(3.703),
                [theme.breakpoints.down(1300)]: {
                  width: theme.spacing(6.5)
                },
                [theme.breakpoints.down('md')]: {
                  width: theme.spacing(5.625)
                },
                [theme.breakpoints.down(360)]: {
                  width: theme.spacing(3.5)
                }
              }
            },
            '& .package-details': {
              background: theme.colors.Pastel,
              padding: theme.spacing(0.625),
              //   minHeight: theme.spacing(3.75),
              borderRadius: theme.spacing(1.875),
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              [theme.breakpoints.down('md')]: {
                borderRadius: theme.spacing(1.875),
                minHeight: theme.spacing(2.0625),
                marginTop: theme.spacing(0.313)
              },
              '& .package-content, & .package-price': {
                display: 'flex',
                alignItems: 'center',
                gap: theme.spacing(0.625)
              },
              '& .package-price': {
                fontSize: theme.spacing(1.158),
                color: '#FFFFFF',
                fontWeight: '600',
                [theme.breakpoints.down(1400)]: {
                  fontSize: theme.spacing(0.875)
                },
                [theme.breakpoints.down('sm')]: {
                  fontSize: theme.spacing(0.75)
                },
                '& img': {
                  width: '25px',
                  [theme.breakpoints.down('sm')]: {
                    width: theme.spacing(1)
                  },
                  '&.free-tag': {
                    [theme.breakpoints.down('md')]: {
                      width: theme.spacing(3)
                    },
                    [theme.breakpoints.down('sm')]: {
                      width: theme.spacing(2)
                    }
                  }
                },
                '&.extra-prize': {
                  [theme.breakpoints.down('sm')]: {
                    display: 'none'
                  }
                },
                '& .scratch-price': {
                  background: 'linear-gradient(180deg, #F7C35C 56.66%, #DD970F 83.52%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontWeight: '700',
                  textShadow: '0px 4px 4px 0px #00000066'
                }
              }
            },
             '& .mob-extra-prize': {
              display: 'none',
              [theme.breakpoints.down('sm')]: {
                display: 'flex',
                marginTop: '0.5rem',
                alignItems: 'center',
                justifyContent: 'center',
                gap: theme.spacing(0.5)
              },
              '& img': {
                width: theme.spacing(1)
              },
              '& .scratch-price': {
                background: 'linear-gradient(180deg, #F7C35C 56.66%, #DD970F 83.52%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '700',
                textShadow: '0px 4px 4px 0px #00000066'
              }
            },
            '& .order-1': {
              [theme.breakpoints.down(1199)]: {
                justifyContent: 'flex-start',
                display: 'flex',
                alignItems: 'center'
              }
            },
            '& .order-2': {
              [theme.breakpoints.down(1199)]: {
                order: 2
              }
            },
            '& .order-3': {
              [theme.breakpoints.down(1199)]: {
                order: 3
              }
            },
            '& .package-img-wrap': {
              display: 'flex',
              alignItems: 'center',
              '& .package-img': {
                position: 'relative',
                [theme.breakpoints.down('sm')]: {
                  width: theme.spacing(3)
                },
                '& .offer-badge-content-text': {
                  top: '45%',
                  //   left: '14%',
                  position: 'absolute',
                  textAlign: 'center',
                  transform: 'translate(-70%, -50%)',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  [theme.breakpoints.down(1500)]: {
                    top: '45%',
                    left: '10%'
                  },

                  // [theme.breakpoints.down(1400)]: {

                  //     left: "0%",
                  // },
                  [theme.breakpoints.down(1199)]: {
                    left: '8%'
                  },

                  [theme.breakpoints.down(767)]: {
                    top: '44%',
                    left: '14%'
                  },

                  [theme.breakpoints.down(460)]: {
                    left: '10%'
                  },
                  '& h5': {
                    fontWeight: 'bold',
                    color: theme.colors.textWhite,
                    fontSize: theme.spacing(0.75),
                    lineHeight: theme.spacing(1),
                    [theme.breakpoints.down('sm')]: {
                      fontSize: theme.spacing(0.75),
                      lineHeight: theme.spacing(0.875)
                    }
                  },
                  '& p': {
                    fontWeight: 'bold',
                    lineHeight: theme.spacing(1),
                    color: theme.colors.textWhite,
                    fontSize: theme.spacing(0.75),
                    [theme.breakpoints.down('sm')]: {
                      fontSize: theme.spacing(0.75),
                      lineHeight: theme.spacing(0.625)
                    }
                  }
                }
              }
            },

            '&:first-child': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #1362BE99)'
              }
            },
            '&:nth-child(2)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #F8D54ECC)'
              }
            },
            '&:nth-child(3)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #3BBE13CC)'
              }
            },
            '&:nth-child(4)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #A613BECC)'
              }
            },
            '&:nth-child(5)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #A613BECC)'
              }
            },
            '&:nth-child(6)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #00A30BCC)'
              }
            },
            '&:nth-child(7)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #C4009DCC)'
              }
            },
            '&:nth-child(8)': {
              '& .bonus-graphics': {
                filter: 'drop-shadow(0px 0px 10px #C44800CC)'
              }
            },
            '& .offer-badge-tag': {
              position: 'absolute',
              left: theme.spacing(-1.3125),
              top: 0,
              zIndex: 1,
              [theme.breakpoints.down('lg')]: {
                left: theme.spacing(-2)
              },
              [theme.breakpoints.down('md')]: {
                left: theme.spacing(-1.825)
              },
              [theme.breakpoints.down('sm')]: {
                left: theme.spacing(-1.875)
              },

              '& img': {
                width: theme.spacing(9.375),
                [theme.breakpoints.down('lg')]: {
                  width: theme.spacing(6.375)
                },
                [theme.breakpoints.down('md')]: {
                  width: theme.spacing(5.375)
                }
              },
              '& .offer-badge-content': {
                position: 'absolute',
                zIndex: 2,
                transform: 'translate(-50%, -50%)',
                left: '50%',
                top: '50%',
                [theme.breakpoints.down('lg')]: {
                  top: '47%'
                },
                '& h5, & p': {
                  fontWeight: theme.typography.fontWeightExtraBold,
                  [theme.breakpoints.down('lg')]: {
                    fontSize: theme.spacing(0.875),
                    textAlign: 'center'
                  },
                  [theme.breakpoints.down('md')]: {
                    fontSize: theme.spacing(0.75),
                    lineHeight: theme.spacing(1)
                  }
                }
              }
            }
          }
        },
        '& .package-btn-wrap': {
          textAlign: 'center',
          display: 'flex',
          alignItems: 'center',
          '& .MuiButtonBase-root': {
            // fontSize: theme.spacing(1.25),
            padding: theme.spacing(0.4, 1.5),
            fontWeight: '600',
            // minWidth: theme.spacing(14.5625),
            // minHeight: theme.spacing(3.25),
            minHeight: theme.spacing(2.407),
            width: '100%',
            [theme.breakpoints.down(1080)]: {
              minWidth: 'auto',
              fontSize: theme.spacing(1),
              //   minHeight: theme.spacing(2.5)
              minHeight: theme.spacing(1.5)
            },

            [theme.breakpoints.down('md')]: {
              //   minHeight: theme.spacing(2.125),
              minHeight: theme.spacing(1.125),
              fontSize: theme.spacing(0.875),
              //   lineHeight: theme.spacing(1),
              padding: theme.spacing(0.4, 0.625)
            }
          }
        }
      },
      '& .inner-heading': {
        marginBottom: theme.spacing(2),
        [theme.breakpoints.down('md')]: {
          marginBottom: theme.spacing(1.125)
        },
        '& h4': {
          fontSize: theme.spacing(1.875),
          fontWeight: '600',
          lineHeight: theme.spacing(2),

          [theme.breakpoints.down('md')]: {
            fontSize: theme.spacing(1.25)
          }
        }
      }
    }
  },

  purchasePopupModal: {
    '& .MuiBackdrop-root': {
      backgroundColor: 'rgba(0, 0, 0, 0.6) !important'
      //   backdropFilter: 'blur(0px)'
    },

    '& .MuiPaper-root': {
      background: theme.colors.testimonalsCardBg,
      borderRadius: `${theme.spacing(1.698)}!important`,
      minWidth: theme.spacing(48),
      maxWidth: theme.spacing(48),

      boxShadow: '0px 4px 4px 0pxrgba(116, 88, 88, 0.4)',
      [theme.breakpoints.down('md')]: {
        minWidth: '80%',
        minHeight: '80%',
        maxHeight: '80%'
      }
    },
    '& .MuiDialogContent-root': {
      background: `url(${otpBg})`,
      backgroundSize: 'cover',
      borderRadius: `${theme.spacing(1.698)}!important`,
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        padding: '1rem'
      },
      '& .close-btn': {
        position: 'absolute',
        top: theme.spacing(0.625),
        right: theme.spacing(0.625),
        color: theme.colors.textWhite,
        zIndex: 2
      },
      '& .modal-header': {
        position: 'relative',
        background: 'linear-gradient(180deg, #FFEA94 16.67%, #FF9A20 80.43%)',
        webkitBackgroundClip: 'text',
        webkitTextFillColor: 'transparent',
        backgroundClip: 'text',
        color: 'transparent',
        fontWeight: 'bold',
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(1.375)
        }
      },
      '& .modal-subheader': {
        color: theme.colors.textWhite,
        fontSize: theme.spacing(1.5625),
        fontWeight: theme.typography.fontWeightBold,
        lineHeight: theme.spacing(2),
        paddingBottom: '0.5rem',
        [theme.breakpoints.down('lg')]: {
          fontSize: theme.spacing(0.875),
          lineHeight: theme.spacing(1.1)
        }
      }
    }
  }
}))
