import React, { useRef, useEffect, useState, useCallback } from 'react'
import useStyles from './GamePlay.styles'
import { Box, Grid, Link, Tooltip, Typography } from '@mui/material'
import BrandLogo from '../../components/ui-kit/icons/brand/brand-logo.webp'
import FavFillIco from '../../components/ui-kit/icons/gameplay/like-fill.webp'
import FavIco from '../../components/ui-kit/icons/gameplay/fav-ico.webp'
import HomePage from '../../components/ui-kit/icons/gameplay/home.svg'
import FullScreen from '../../components/ui-kit/icons/gameplay/full-screen.webp'
import { CasinoQuery, GeneralQuery, useGetGameLink, useGetProfileMutation } from '../../reactQuery'
import {
  useCoinStore,
  usePortalStore,
  useSelectedProviderStore,
  useGamesStore,
  useSiteLogoStore,
  useSubCategoryOnLoadStore,
  useUserStore
} from '../../store/store'
import { useParams, useNavigate, useLocation } from 'react-router-dom'
import { toast } from 'react-hot-toast'
import { useFavToggleMutation } from '../../reactQuery/casinoQuery'
import useGetDeviceType from '../../utils/useGetDeviceType'

import Loader from '../../components/Loader'
import { customEvent } from '../../utils/optimoveHelper'
import MenuIcon from '@mui/icons-material/Menu'
import CloseIcon from '@mui/icons-material/Close'

import TournamentPopup from '../TournamentsPage/TournamentPopup'
import { tournamentEndSocket } from '../../utils/socket'
import TournamentEndPopup from '../TournamentsPage/TournamentEndPopup'
import CmsModal from '../../components/CmsModal/CmsModal'
import { joinTournament } from '../../utils/apiCalls'
import { useMutation } from '@tanstack/react-query'
import { KeyTypes } from '../../reactQuery/KeyTypes'
import PaymentStatus from '../../components/PaymentStatus'
import TagManager from 'react-gtm-module'
import { getLoginToken } from '../../utils/storageUtils'
import { PlayerRoutes } from '../../routes'
import Signin from '../ForgotPassword'
import MobileVerification from '../MobileVerification'
import usdCash from '../../components/ui-kit/icons/utils/usd-cash.webp'
import tmfjackpot from '../../components/ui-kit/icons/png/tmf-jackpot.png'
import CustomSwitch from '../../components/CustomSwitch'
import { useJackpotStore } from '../../store/useJackpotStore'
import jackpotQuery from '../../reactQuery/jackpotQuery'
import useLiveWinners from '../LandingPage/hooks/useLiveWiners'
import sidebarInfo from '../../components/ui-kit/icons/svg/sidebar-info.svg'
import JackpotOptInPopUp from '../Jackpot/components/JackpotOptInPopUp'
import JackpotMultiplier from '../Jackpot/components/JackpotMultiplier'
import { debounceFn } from '../../utils/lodashLite'
/* eslint-disable multiline-ternary */

const DIGIT_HEIGHT = 32
const MOBILE_DIGIT_HEIGHT = 22

const RollingDigit = ({ digit, isMobile, animate }) => {
  const classes = useStyles()
  const height = isMobile ? MOBILE_DIGIT_HEIGHT : DIGIT_HEIGHT

  return (
    <div className={classes.digitContainer} style={{ height }}>
      <div
        className={classes.digitStrip}
        style={{
          transform: `translateY(-${digit * height}px)`,
          transition: animate ? 'transform 0.6s ease-out' : 'none'
        }}
      >
        {Array.from({ length: 10 }, (_, i) => (
          <div key={i} className={classes.digit} style={{ height }}>
            {i}
          </div>
        ))}
      </div>
    </div>
  )
}

const GamePlay = () => {
  const location = useLocation()
  const classes = useStyles()
  const [gameUrl, setGameUrl] = useState('')
  const [isFavorite, setIsFavorite] = useState(null)
  const navigate = useNavigate()
  const { isMobile } = useGetDeviceType()
  const [isLoading, setIsLoading] = useState(false)
  const subCategoryState = useSubCategoryOnLoadStore((state) => state)
  const selectedProviderStore = useSelectedProviderStore((state) => state)
  const auth = useUserStore((state) => state)
  const { gameId: masterCasinoGameId, tournamentId: tournamentId } = useParams()
  const { name, gameType } = location.state || {}
  const setSelectedSubCat = useGamesStore((state) => state.setSelectedSubCat)
  const { setGameName, tournamentEndSocketConnection } = useUserStore((state) => ({
    setGameName: state.setGameName,
    tournamentEndSocketConnection: state.tournamentEndSocketConnection
  }))
  const { refetch } = useLiveWinners()
  const { isScTournamentTermsAccepted, isGcTournamentTermsAccepted } = auth?.userDetails || {}

  const [activeTournamentId, setActiveTournamentId] = useState()
  const portalStore = usePortalStore((state) => state)
  const [tournamentList, setTournamentList] = useState([])
  const logoData = useSiteLogoStore((state) => state)
  const coinType = useCoinStore((state) => {
    return state.coinType
  })

  const setCoin = useCoinStore((state) => {
    return state.setCoinType
  })

  const [coinTypeState, setCoinTypeState] = useState(coinType)
  const [menuOpen, setMenuOpen] = useState(false)

  useEffect(() => {
    if (tournamentEndSocketConnection) {
      tournamentEndSocket.on('TOURNAMENT_END', (tourData = {}) => {
        const { data } = tourData
        if (data?.tournamentId && data?.tournamentEndingSoon) {
          portalStore.openPortal(
            () => (
              <TournamentEndPopup
                open={true}
                onClose={() => {
                  portalStore.closePortal()
                  navigateToLobby()
                }}
                onPlayOutside={() => {
                  portalStore.closePortal()
                }}
                onClickBackToLobby={() => {
                  portalStore.closePortal()
                  navigateToLobby()
                }}
              />
            ),
            'tournamentEndPopup'
          )
        }
      })
    }
    return () => {
      tournamentEndSocket.off('TOURNAMENT_END', () => {})
    }
  }, [tournamentEndSocketConnection])

  useEffect(() => {
    setGameName(name)
  }, [name])

  const options = usePortalStore((state) => state.options)

  const user = useUserStore((state) => state)

  const checkSession = GeneralQuery.getCheckSessionQuery()
  const gameContainerRef = useRef(null)
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      user.setUserDetails(res?.data?.data)
    },
    onError: (error) => {
      console.log('error', error)
    }
  })

  const toggleFullscreen = () => {
    const element = gameContainerRef.current
    if (!document.fullscreenElement) {
      if (element.requestFullscreen) {
        element.requestFullscreen()
      } else if (element.mozRequestFullScreen) {
        // Firefox
        element.mozRequestFullScreen()
      } else if (element.webkitRequestFullscreen) {
        // Chrome, Safari and Opera
        element.webkitRequestFullscreen()
      } else if (element.msRequestFullscreen) {
        // IE/Edge
        element.msRequestFullscreen()
      } else if (element.webkitEnterFullscreen) {
        // iPhone/iPad Safari
        element.webkitEnterFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.mozCancelFullScreen) {
        // Firefox
        document.mozCancelFullScreen()
      } else if (document.webkitExitFullscreen) {
        // Chrome, Safari and Opera
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        // IE/Edge
        document.msExitFullscreen()
      }
    }
  }

  const navigateToLobby = () => {
    checkSession.mutate()
    setSelectedSubCat('Lobby')
    refetch()
    getProfileMutation.mutate()
    window.localStorage.removeItem('isGameplay')
    window.localStorage.removeItem('GameID')
    navigate('/')
  }

  const successJoinToggler = (data) => {
    toast.success('Tournament Joined successfully.')
    portalStore.closePortal()
  }
  const errorJoinToggler = () => {}

  const useJoinTournamentMutation = ({ successJoinToggler, errorJoinToggler }) => {
    return useMutation({
      mutationKey: [KeyTypes.JOIN_TOURNAMENT],
      mutationFn: (data) => joinTournament(data),
      onSuccess: (data) => {
        successJoinToggler && successJoinToggler(data)

        // Set the active tournament ID
        setActiveTournamentId(activeTournamentId)

        let cType = coinType // Default coin type

        // Step 2: Check if the current tournament has a different entry coin
        if (activeTournamentId) {
          const currentTournament = tournamentList?.find(({ tournamentId: tId }) => tId === activeTournamentId)

          if (currentTournament && currentTournament?.entryCoin !== coinType) {
            // Step 3: Switch the coin type if needed
            setCoin(currentTournament?.entryCoin) // Update the coin in the store
            cType = currentTournament?.entryCoin // Set the new coin type for the game

            // Show a toast indicating the coin switch
            toast(`You've successfully switched to ${currentTournament?.entryCoin} coin!`)
          }
        }
        handleGameTournamentLaunch(masterCasinoGameId, activeTournamentId, cType)
      },
      onError: (error) => {
        errorJoinToggler && errorJoinToggler(error)
      }
    })
  }
  const mutationJoinTournament = useJoinTournamentMutation({
    successJoinToggler,
    errorJoinToggler
  })

  const handleConfirm = (data, tournamentId) => {
    if (data === 'submit' && tournamentId !== 0) {
      setActiveTournamentId(tournamentId)
      const payload = {
        tournamentId: tournamentId,
        isTournamentTermsAccepted: true
      }
      mutationJoinTournament.mutate(payload)
    }
  }

  const handleJoinTournament = (pathname, tournamentId, entryCoin) => {
    const shouldOpenModal =
      (entryCoin === 'SC' && !isScTournamentTermsAccepted) || (entryCoin === 'GC' && !isGcTournamentTermsAccepted)

    if (shouldOpenModal) {
      portalStore.openPortal(
        () => (
          <CmsModal
            istournamentpopup={true}
            path={pathname}
            handleConfirm={(data) => {
              handleConfirm(data, tournamentId)
            }}
          />
        ),
        'cmsModal'
      )
    } else {
      handleConfirm('submit', tournamentId) // Directly confirm if already accepted
    }
  }

  const renderTournamentPopup = (tournaments) => {
    portalStore.openPortal(
      () => (
        <TournamentPopup
          open={true}
          onClose={() => {
            navigateToLobby()
            portalStore.closePortal()
          }}
          tournamentData={tournaments}
          hanlePlayWithoutTournament={() => {
            getGameLinkMutation({
              coin: coinType,
              gameId: parseInt(masterCasinoGameId),
              isDemo: false,
              isMobile: isMobile,
              tournamentCheck: false
            })
            portalStore.closePortal()
          }}
          handlePlayInTournament={handlePlayInTournament}
          handleJoinTournament={handleJoinTournament}
        />
      ),
      'tournamentPopup'
    )
  }

  const handlePlayNow = (masterCasinoGameId) => {
    const isLoggedIn = !!getLoginToken() || auth?.isAuthenticate
    const wallet = auth?.userDetails?.userWallet
    const totalScCoin = wallet?.totalScCoin ?? 0
    const totalGcCoin = wallet?.gcCoin ?? 0
    if (isLoggedIn) {
      if ((coinType === 'SC' && totalScCoin > 0) || (coinType === 'GC' && totalGcCoin > 0)) {
        handleGameLaunch(masterCasinoGameId)
      } else {
        toast.error('Please make a purchase!')
        navigate(PlayerRoutes.Store)
      }
    } else {
      portalStore.openPortal(() => <Signin />, 'loginModal')
    }
  }

  const handleCloseMobileVerification = () => {
    toast.error('Phone verification is required to play using SC', 'error')
    navigateToLobby()
  }

  // const [jackpotOn, setJackpotOn] = useState(true) // NEW STATE
  // SETTERS - JACKPOT
  const { setJackpotOn, setJackpotData, setJackpotMultiplier } = useJackpotStore()

  // GET JACKPOT DATA
  const { data: jackpotInitData } = jackpotQuery.getJackpotDataQuery({
    params: {
      jackpotPage: false
    },
    successToggler: (data) => {
      setJackpotData('jackpotPoolAmount', Number(data?.jackpotPoolAmount))
      setJackpotData('entryAmount', data?.entryAmount)
      setJackpotData('recentJackpotWinners', data?.recentJackpotWinners)
    }
  })

  const { jackpotOn, jackpotData, newJackpot, jackpotPoolAmount, jackpotMultiplier } = useJackpotStore((state) => ({
    jackpotOn: state.jackpotOn,
    jackpotData: state.jackpotData,
    newJackpot: state.newJackpot,
    jackpotPoolAmount: state.jackpotData.jackpotPoolAmount,
    jackpotMultiplier: state.jackpotMultiplier
  }))
  const [localMultiplier, setLocalMultiplier] = useState(jackpotMultiplier)

  useEffect(() => {
    setLocalMultiplier(jackpotMultiplier)
  }, [jackpotMultiplier])

  // OPT JACKPOT
  const toggleJackpotSuccess = (data, variables) => {
    setJackpotOn(variables?.status)
    setJackpotMultiplier(variables?.multiplier)
  }

  const toggleJackpotError = (err) => {
    console.log('$$$ToggleJACKPOT_ERR', err)
  }

  const toggleJackpot = jackpotQuery.useJackpotOptInMutation({
    onSuccess: toggleJackpotSuccess,
    onError: toggleJackpotError
  })

  const handleToggle = () => {
    toggleJackpot.mutate({
      status: !jackpotOn,
      multiplier: jackpotMultiplier
    })
  }

  const debounceToggle = useCallback(debounceFn((newValue) => {
    toggleJackpot.mutate({
      status: true,
      multiplier: newValue
    })
  }, 500),
  [])

  const handleMultiplierChange = (newValue) => {
    setLocalMultiplier(newValue)
    debounceToggle(newValue)
  }

  const { mutate: getGameLinkMutation, refetch: fetchData } = useGetGameLink({
    onSuccess: (res) => {
      const { data: { tournaments = [], success, gameUrl, isFavorite } = {} } = res
      if (success === false) {
        navigateToLobby()
      }
      if ((!jackpotOn || jackpotMultiplier < 6) && jackpotInitData?.jackpotPoolAmount > 0 && coinType === 'SC') {
        portalStore.openPortal(
          () => <JackpotOptInPopUp toggleJackpot={toggleJackpot} entryAmount={jackpotData?.entryAmount} />,
          'JackpotOptIn'
        )
      }
      if (tournaments?.length) {
        renderTournamentPopup(tournaments)

        setTournamentList(tournaments)
      } else {
        setGameUrl(gameUrl)
        setIsFavorite(isFavorite)
      }
      window.localStorage.setItem('isGameplay', true)
      window.localStorage.setItem('GameID', masterCasinoGameId)
      setIsLoading(false)
    },
    onError: (err) => {
      if (err?.response?.data?.errors?.length) {
        const { errors } = err.response.data
        if (errors?.[0]?.name === 'PhoneNotVerified') {
          portalStore.openPortal(
            () => (
              <MobileVerification
                calledFor='gamePlayMobileVerification'
                handlePlayNow={() => handlePlayNow(masterCasinoGameId, name)}
                onClose={handleCloseMobileVerification}
              />
            ),
            'innerModal'
          )
        } else {
          navigateToLobby()
        }
      }
    }
  })

  const handlePlayInTournament = (tournamentId, tournamentData) => {
    portalStore.closePortal()
    setActiveTournamentId(tournamentId)
    let cType = coinType
    if (tournamentId) {
      const currentTournament = tournamentData?.find(({ tournamentId: tId }) => tId === tournamentId)

      if (currentTournament && currentTournament?.entryCoin !== coinType) {
        setCoin(currentTournament?.entryCoin)
        cType = currentTournament?.entryCoin
        toast(`You've successfully switched to ${currentTournament?.entryCoin} coin!`)
      }
    }
    handleGameTournamentLaunch(masterCasinoGameId, tournamentId, cType)
  }

  useEffect(() => {
    if (coinTypeState !== coinType) {
      setCoinTypeState(coinType)
      if (activeTournamentId && gameUrl) {
        // tournament popup will display
        setGameUrl('')
        setIsLoading(true)
        // this method will pass the tournament check flag as true
        handleGameLaunch(masterCasinoGameId)
      } else if (gameUrl) {
        // relaunching the game
        setGameUrl('')
        getGameLinkMutation({
          coin: coinType,
          gameId: parseInt(masterCasinoGameId),
          isDemo: false,
          isMobile: isMobile,
          tournamentCheck: false
        })
      }
      return
    }

    if (tournamentId) {
      if (masterCasinoGameId && tournamentId && [true, false]?.includes(options?.refetchGameUrl)) {
        setIsLoading(true)
        handleGameTournamentLaunch(masterCasinoGameId, tournamentId)
      }
    } else {
      if (masterCasinoGameId && [true, false]?.includes(options?.refetchGameUrl)) {
        if (!activeTournamentId) {
          setIsLoading(true)
          handleGameLaunch(masterCasinoGameId)
          const parameters = {
            game_id: masterCasinoGameId,
            name: name
            // game_type: gameType
          }
          if (import.meta.env.VITE_NODE_ENV === 'production') {
            customEvent('game_launch', parameters, user?.userDetails?.userId)
          }
        }
      }
    }
  }, [coinType, options?.refetchGameUrl, masterCasinoGameId])

  const handlePaymentSuccess = (data) => {
    portalStore.openPortal(() => <PaymentStatus paymentDetails={data} />, 'paymentmodal')
  }

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const statuss = params.get('status')

    if (statuss === 'success' || statuss === 'failed' || statuss === 'cancelled') {
      const data = {
        transactionId: params.get('transactionId'),
        status: params.get('status'),
        paymentMethod: params.get('paymentMethod'),
        scCoin: params.get('scCoin'),
        gcCoin: params.get('gcCoin'),
        bonusSc: params.get('bonusSc'),
        bonusGc: params.get('bonusGc'),
        amount: params.get('amount')
      }
      handlePaymentSuccess(data)
    }
  }, [location])

  const handleGameLaunch = useCallback(
    async (gameId) => {
      getGameLinkMutation({
        coin: coinType,
        gameId: parseInt(gameId),
        isDemo: false,
        isMobile: isMobile,
        tournamentCheck: true
      })
    },
    [coinType]
  )
  const handleGameTournamentLaunch = useCallback(
    async (gameId, tournamentId, cType) => {
      getGameLinkMutation({
        coin: cType || coinType,
        gameId: parseInt(gameId),
        tournamentId: parseInt(tournamentId),
        isDemo: false,
        isMobile: isMobile,
        tournamentCheck: false
      })
    },
    [coinType]
  )
  const mutationFavToggle = useFavToggleMutation({
    onSuccess: (res) => {
      setIsFavorite(!isFavorite)
      if (selectedProviderStore.selectedProviderId !== '') {
        subcategoryListMutation.mutate({ masterCasinoProviderId: selectedProviderStore.selectedProviderId })
      } else {
        subcategoryListMutation.mutate()
      }
      if (isFavorite) {
        toast.success('Removed from Favorites')
      } else {
        toast.success('Added to Favorites')
      }
    },
    onError: (error) => {
      if (error?.response?.data?.errors.length > 0) {
        const { errors } = error.response.data
        errors.forEach((error) => {
          console.log(error)
        })
      }
    }
  })

  const toggleFav = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'favorite_game',
        game_Id: +masterCasinoGameId,
        favorite_status: !isFavorite,
        user_id: user?.userDetails?.userId,
        email: user?.userDetails?.email
      }
    })
    let parameters = {
      game_Id: +masterCasinoGameId,
      favorite_status: !isFavorite,
      email: user?.userDetails?.email
    }
    if (import.meta.env.VITE_NODE_ENV === 'production') {
      customEvent('mark_favorite_game', parameters, user?.userDetails?.userId)
    }
    mutationFavToggle.mutate({ request: !isFavorite, gameId: +masterCasinoGameId })
  }
  /* success response of subcategoryListMutation */
  const successToggler = (data) => {
    subCategoryState.setSubCategories(data?.data?.data)
  }
  /* error response of subcategoryListMutation */
  const errorToggler = () => {}
  /* subcategoryListMutation api hit  */
  const subcategoryListMutation = CasinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler
  })

  const [count, setCount] = useState(jackpotPoolAmount || 0)
  const [hasAnimated, setHasAnimated] = useState(false)

  // const targetNumber = 230948
  const duration = 2000 // in ms
  const frameRate = 30 // frames per second

  useEffect(() => {
    const newAmount = jackpotPoolAmount
    if (newAmount == null || newAmount === count) return

    // If this is the first time (initial render), skip animation
    if (!hasAnimated) {
      setCount(newAmount)
      setHasAnimated(true)
      return
    }

    const startAmount = count
    const difference = newAmount - startAmount
    const totalFrames = duration / (1000 / frameRate)
    let currentFrame = 0

    const interval = setInterval(() => {
      currentFrame++
      const progress = currentFrame / totalFrames
      const eased = easeOutQuad(progress)
      const currentCount = +(startAmount + difference * eased)

      setCount(currentCount)
      if (currentFrame >= totalFrames) {
        clearInterval(interval)
        setCount(newAmount)
      }
    }, 1000 / frameRate)

    function easeOutQuad(t) {
      return t * (2 - t)
    }

    return () => clearInterval(interval)
  }, [jackpotPoolAmount])

  const countStr = Number(count).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })

  const onLinkClick = (pathname) => {
    portalStore.openPortal(() => <CmsModal path={pathname} />, 'cmsModal')
  }

  return (
    <div ref={gameContainerRef} style={{ width: '100%', height: '100vh', border: '1px solid black' }}>
      <Grid className={classes.lobbyRight}>
        {isLoading && <Loader />}
        <Grid className='game-play-wrap'>
          <Grid className='game-frame'>
            {/* {jackpotWin && (
              <Grid className='payment-gif'>
                <Lottie className='popper-animation' animationData={popperAnimation} loop />
              </Grid>
            )} */}
            <iframe
              key='gameIframe'
              allow='allowfullscreen; webkitallowfullscreen; mozallowfullscreen; fullscreen; geolocation *; bluetooth; accelerometer; gyroscope;'
              allowfullscreensandbox='allow-same-origin allow-scripts allow-forms'
              title='evo_bloodandshadow20 by evolution'
              lang='en-US-x-sweep'
              height='100%'
              src={gameUrl || ''}
              width='100%'
            />
          </Grid>
          <Grid className='game-play-cta-wrap'>
            <Grid className='game-play-cta-left'>
              <Tooltip title='Back' arrow>
                {/* <Link>
                  <img src={HomePage} alt='Arrow' onClick={() => navigateToLobby()} />
                </Link> */}
                <Link onClick={navigateToLobby}>
                  <img className='icon-btn' src={HomePage} alt='Home' />
                </Link>
              </Tooltip>
              <Tooltip title='Favorite' arrow>
                <Link onClick={() => toggleFav()}>
                  {/* {!isFavorite ? (
                    <img className='casinoGame-fav' src={FavIco} />
                  ) : (
                    <img className='casinoGame-fav' alt='heart' src={FavFillIco} />
                  )} */}
                  <img className='icon-btn' src={isFavorite ? FavFillIco : FavIco} alt='Favorite' />
                </Link>
              </Tooltip>
              <Tooltip title='Full Screen' arrow>
                <Link onClick={toggleFullscreen}>
                  {/* <img className='casinoGame-fav' alt='heart' src={FullScreen} /> */}
                  <img className='icon-btn' src={FullScreen} alt='Full Screen' />
                </Link>
              </Tooltip>
            </Grid>

            {/* Mobile-only hamburger and floating menu */}
            <Grid className='mobile-menu-wrapper'>
              <Box className='hamburger-toggle' onClick={() => setMenuOpen(!menuOpen)}>
                {menuOpen ? <CloseIcon /> : <MenuIcon />}
              </Box>

              <Box className={`mobile-floating-menu ${menuOpen ? 'open' : ''}`}>
                <Tooltip title='Full Screen' arrow>
                  <Link onClick={toggleFullscreen}>
                    <img className='icon-btn' src={FullScreen} alt='Full Screen' />
                  </Link>
                </Tooltip>
                <Tooltip title='Favorite' arrow>
                  <Link onClick={toggleFav}>
                    <img className='icon-btn' src={isFavorite ? FavFillIco : FavIco} alt='Favorite' />
                  </Link>
                </Tooltip>

                <Tooltip title='Back' arrow>
                  <Link onClick={navigateToLobby}>
                    <img className='icon-btn' src={HomePage} alt='Home' />
                  </Link>
                </Tooltip>
              </Box>
            </Grid>
            {jackpotInitData?.jackpotPoolAmount > 0 && (
              <Box className='jackpot-grid '>
                <div className='jackpot-badge'>
                  <img src={usdCash} className='cash-icon' alt='cash' />
                  {/* <Typography>{formatToTwoDecimals(count.toLocaleString('en-IN'))} SC</Typography> */}
                  <Typography component='div' className={classes.digitsWrapper}>
                    {countStr.split('').map((char, idx) =>
                      /\d/.test(char) ? (
                        hasAnimated ? (
                          <RollingDigit key={idx} digit={parseInt(char, 10)} isMobile={isMobile} />
                        ) : (
                          <span key={idx} className={classes.digit}>
                            {char}
                          </span>
                        )
                      ) : (
                        <span key={idx} className={classes.digit}>
                          {char}
                        </span>
                      )
                    )}
                    <span className={classes.scText}>SC</span>
                  </Typography>
                  <img src={tmfjackpot} className='tmf-jackpot' alt='jackpot crown' />
                </div>

                <Box className='mode-wrap jackpot-enabled'>
                  {!jackpotOn ? (
                    <div className='jackpot-mode'>
                      <div className={`jackpot-box ${jackpotOn ? 'jackpot-on' : 'jackpot-off'}`}>
                        <Typography>Jackpot MODE</Typography>
                        <CustomSwitch checked={jackpotOn} onChange={handleToggle} />
                      </div>
                    </div>
                  ) : (
                    <Box style={{ marginTop: '30px' }}>
                      <JackpotMultiplier mode='gameplay' value={localMultiplier} onChange={handleMultiplierChange} />
                    </Box>
                  )}
                  {newJackpot ? (
                    <Typography style={{ fontSize: '14px', lineHeight: 1 }} variant='h5'>
                      Jackpot completed, new one active now
                    </Typography>
                  ) : (
                    <>
                      {isMobile ? (
                        <Typography variant='h5'>
                          {parseFloat((jackpotData?.entryAmount * jackpotMultiplier).toFixed(2)) || 0} SC/spin deducted
                          ( jackpot Mode enabled )
                          <Tooltip title='Terms and Conditions' arrow>
                            <span>
                              {' '}
                              <Link href='javascript:void(0);' onClick={() => onLinkClick('/cms/jackpot')}>
                                <img src={sidebarInfo} style={{ width: '1rem', position: 'relative', top: '5px' }} />
                              </Link>
                            </span>
                          </Tooltip>
                        </Typography>
                      ) : (
                        <>
                          <Typography variant='h5'>
                            {parseFloat((jackpotData?.entryAmount * jackpotMultiplier).toFixed(2)) || 0} SC per spin
                            will be deducted when Jackpot Mode is enabled.
                            <Tooltip title='Terms and Conditions' arrow>
                              <Link href='javascript:void(0);' onClick={() => onLinkClick('/cms/jackpot')}>
                                <img src={sidebarInfo} style={{ width: '1.5rem', position: 'relative', top: '8px' }} />
                              </Link>
                            </Tooltip>
                          </Typography>
                        </>
                      )}
                    </>
                  )}
                </Box>
              </Box>
            )}
            <Link className='game-play-logo'>
              <img src={logoData?.desktopLogo || BrandLogo} alt='Logo' />
            </Link>
          </Grid>
        </Grid>
      </Grid>
    </div>
  )
}

export default GamePlay
