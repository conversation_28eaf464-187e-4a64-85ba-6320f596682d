import React, { useState } from 'react'
import { Box, Grid, MenuItem, Select, Tab, Tabs } from '@mui/material'
import useStyles from '../TournamentsPage/Tournaments.styles'
import { useNavigate, Link } from 'react-router-dom'
import PropTypes from 'prop-types'
import TabTournament from './components/TabTournament'
import { useBannerStore } from '../../store/useBannerSlice'
import BannerManagement from '../../components/BannerManagement'
import usdIcon from '../../components/ui-kit/icons/opImages/usd.svg'
import usdchipIcon from '../../components/ui-kit/icons/opImages/usd-chip.svg'
import { tournamentQuery } from '../../reactQuery/tournamentQuery'
import JackpotBadge from '../Jackpot/JackpotBadge'
import { useUserStore } from '../../store/useUserSlice'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <>{children}</>}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  }
}

const TournamentsPage = () => {
  const classes = useStyles()

  const navigate = useNavigate()

  const [value, setValue] = useState(0)
  const [coinType, setCoinType] = useState('SC')
  const [selectedOption, setSelectedOption] = useState('option1')

  const { userDetails } = useUserStore((state) => ({
    userDetails: state?.userDetails,
  }))

  const { data: tournamentData } = tournamentQuery.getTournamentQuery({
    params: { coinType: coinType }
  })

  const ongoingCount = tournamentData?.data?.runningTournaments?.count || 0
  const upcomingCount = tournamentData?.data?.upcomingTournaments?.count || 0
  const joinedCount = tournamentData?.data?.joinedTournaments?.count || 0
  const historyCount = tournamentData?.data?.oldTournaments?.count || 0

  const { tournamentPage } = useBannerStore((state) => state)

  const userSubscription = useSubscriptionStore((state) => state.userSubscription)

  const handleChange = (event, newValue) => {
    setValue(newValue) // Change the active tournament tab
  }

  const handleChangeCoinType = (event, newValue) => {
    if (newValue === 0) {
      setCoinType('GC')
    } else {
      setCoinType('SC')
    }
  }

  const handleTournamentDetail = (e, tabKey) => {
    navigate(`/tournament-detail/${tabKey}/${e}`)
  }

  const handleSelectChange = (event) => {
    const selectedValue = event?.target?.value
    setSelectedOption(selectedValue)

    // Update the active tab based on the dropdown selection
    if (selectedValue === 'option1') {
      setValue(0) // OnGoing
    } else if (selectedValue === 'option2') {
      setValue(1) // Upcoming
    } else if (selectedValue === 'option3') {
      setValue(2) // Joined
    } else if (selectedValue === 'option4') {
      setValue(3) // History
    }
  }

  const hasSubscription = userSubscription?.subscriptionDetail !== null
  const tournamentSubscriberOnly = userSubscription?.subscriptionFeatureDetail?.TOURNAMENT_SUBSCRIBER_ONLY
  const tournamentSubscriberOnlyMax = userSubscription?.subscriptionFeatureMaxValue?.TOURNAMENT_SUBSCRIBER_ONLY
  const tournamentDiscountPercentage = userSubscription?.subscriptionFeatureDetail?.TOURNAMENT_JOINING_FEE_DISCOUNT
  const maxTournamentDiscountPercentage = userSubscription?.subscriptionFeatureMaxValue?.TOURNAMENT_JOINING_FEE_DISCOUNT

  const CheckSubscription = () => {
    if (!tournamentSubscriberOnlyMax) return null

    if (hasSubscription && tournamentSubscriberOnly) {
      return (
        <Box className={classes.subscriberBanner}>
          <span>
            🎉 You’re in! As a proud <strong className='tmfPlus'>TMF Plus member</strong>,
            you’ve unlocked <strong>{tournamentDiscountPercentage}% OFF</strong> your tournament entry fee
            and also a chance to win <strong className='tmfPlus'>Vegas Vacation Giveaway Tournament</strong>.
            Play smart, pay less, and win big! 🏆
          </span>
        </Box>
      )
    }

    if (!hasSubscription && !tournamentSubscriberOnly) {
      return (
        <Box className={classes.subscriberBanner}>
          <span>
            Become a <strong className='tmfPlus'>TMF Plus member</strong> today and get up to
            <strong> {maxTournamentDiscountPercentage}% OFF</strong> entry fees and also a chance to win
            <strong className='tmfPlus'> Vegas Vacation Giveaway Tournament</strong>.
          </span>
          <Link to='/subscriptions' className='joinNow'>JOIN NOW</Link>
        </Box>
      )
    }

    if (hasSubscription && !tournamentSubscriberOnly) {
      return (
        <Box className={classes.subscriberBanner}>
          <span>
            Upgrade your membership and unlock up to
            <strong> {maxTournamentDiscountPercentage}% OFF</strong> entry fees and also a chance to win
            <strong className='tmfPlus'> Vegas Vacation Giveaway Tournament</strong>.
            Don’t miss your chance to save and compete! 💥
          </span>
          <Link to='/subscriptions' className='joinNow'>UPGRADE NOW</Link>
        </Box>
      )
    }

    return null
  }

  return (
    <Grid className={classes.lobbyRight}>
      <Grid
        className={[classes.wrapper, 'promotions-wrap'].join(' ')}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '2rem'
        }}
      >
        <Grid>
          <BannerManagement bannerData={tournamentPage} />
        </Grid>
        <Box className={classes.switchTabWrap}>
          <Box className='filter-wrap'>
            <Select
              value={selectedOption}
              onChange={handleSelectChange}
              inputProps={{ 'aria-label': 'Without label' }}
              sx={{ minWidth: 130 }}
              MenuProps={{
                PaperProps: {
                  className: 'custom-select-paper' // Apply custom styles to the menu
                }
              }}
            >
              <MenuItem value='option1'>{`OnGoing (${ongoingCount})`}</MenuItem>
              <MenuItem value='option2'>{`Upcoming (${upcomingCount})`}</MenuItem>
              <MenuItem value='option3'>{`Joined (${joinedCount})`}</MenuItem>
              <MenuItem value='option4'>{`History (${historyCount})`}</MenuItem>
            </Select>
            <Box className='custom-tabs'>
              <Tabs
                value={value}
                onChange={handleChange}
                aria-label='basic tabs example'
                variant='scrollable'
                scrollButtons='auto'
              >
                <Tab label={`OnGoing (${ongoingCount})`} {...a11yProps(0)} />
                <Tab label={`Upcoming (${upcomingCount})`} {...a11yProps(1)} />
                <Tab label={`Joined (${joinedCount})`} {...a11yProps(2)} />
                <Tab label={`History (${historyCount})`} {...a11yProps(3)} />
              </Tabs>
            </Box>
            <Grid className={classes.switchTabWrap}>
              <Tabs value={coinType === 'GC' ? 0 : 1} onChange={handleChangeCoinType} aria-label='basic tabs example'>
                <Tab
                  icon={
                    <div>
                      <img src={usdchipIcon} alt='coinIcon' className='image1' />
                    </div>
                  }
                  iconPosition='start'
                  label='Gold coin'
                  className='gold-coin-tab'
                />

                <Tab
                  icon={
                    <div>
                      <img src={usdIcon} alt='walletIcon' className='image1' />
                    </div>
                  }
                  iconPosition='start'
                  className='shweep-coin-tab'
                  label='Sweep Coin'
                />
              </Tabs>
            </Grid>
          </Box>

          <CheckSubscription />

          <Box className='profile-section-detail'>
            <CustomTabPanel value={value} index={0}>
              <TabTournament
                tabKey={'onGoing'}
                data={tournamentData?.data?.runningTournaments?.rows}
                handleTournamentDetail={handleTournamentDetail}
                coinType={coinType}
              />
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
              <TabTournament
                tabKey={'upComing'}
                data={tournamentData?.data?.upcomingTournaments?.rows}
                handleTournamentDetail={handleTournamentDetail}
                coinType={coinType}
              />
            </CustomTabPanel>

            <CustomTabPanel value={value} index={2}>
              <TabTournament
                tabKey={'joined'}
                // data={tournamentData?.data?.joinedTournaments?.rows?.filter(x => new Date(x?.endDate) > new Date())}
                data={tournamentData?.data?.joinedTournaments?.rows}
                handleTournamentDetail={handleTournamentDetail}
                coinType={coinType}
              />
            </CustomTabPanel>

            <CustomTabPanel value={value} index={3}>
              <TabTournament
                tabKey='old'
                data={tournamentData?.data?.oldTournaments?.rows}
                handleTournamentDetail={handleTournamentDetail}
                coinType={coinType}
              />
            </CustomTabPanel>
          </Box>
        </Box>
      </Grid>
      <JackpotBadge />
    </Grid>
  )
}

export default TournamentsPage
