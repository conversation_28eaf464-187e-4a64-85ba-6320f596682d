import { makeStyles } from '@mui/styles'

import { counterBg, otpBg } from '../../components/ui-kit/icons/webp'
import { ButtonPrimary, LobbyRight, mirrorText, switchTabWrap } from '../../MainPage.styles'


export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme)
  },

  wrapper: {
    maxWidth: '1272px',
    margin: '0 auto',
    width: '100%',

    [theme.breakpoints.down(576)]: {
      gap: '1rem !important'
    },
    '& .becomePartner': {
      ...ButtonPrimary(theme),
      padding: '15px 30px',
      textDecoration: 'none',
      marginTop: '20px',
      display: 'inline-block',
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        padding: '15px 30px'
      },
      [theme.breakpoints.down('sm')]: {
        padding: '8px 16px',
        marginTop: '10px'
      }
    },
    '& .banner': {
      position: 'relative',
      backgroundSize: 'cover',
      minHeight: theme.spacing(22.0625),
      borderRadius: theme.spacing(1.25),
      overflow: 'hidden',
      padding: theme.spacing(0.313, 5),
      [theme.breakpoints.down('sm')]: {
        minHeight: theme.spacing(8.75),
        padding: theme.spacing(1, 2),
        borderRadius: theme.spacing(0.625),
        backgroundPosition: '43% 80%'
      },
      '& .banner-img': {
        position: 'relative',
        top: theme.spacing(0.625),
        textAlign: 'center'
      },
      '& .bannerText': {
        '& p': {
          fontSize: theme.spacing(4),
          textTransform: 'capitalize',
          fontWeight: theme.typography.fontWeightBoldBlack,
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1.4375),
            padding: '0'
          }
        }
      }
    }
  },
  tournamentItems: {
    // backgroundImage: `url(${TournamentOne})`,
    // backgroundImage: "url('../../../src/components/ui-kit/icons/webp/tournament-4.webp')",
    borderRadius: '1rem',
    backgroundSize: '100% 100%',
    backgroundRepeat: 'no-repeat',
    padding: '2rem 3rem',
    display: 'flex',
    cursor: 'pointer',
    flexDirection: 'column',
    position: 'relative',
    overflow: 'hidden',
    alignItems: 'start',
    justifyContent: 'center',
    [theme.breakpoints.down('md')]: {
      padding: '1.5rem 2.5rem'
    },
    [theme.breakpoints.down('sm')]: {
      padding: '2rem',
      aspectRatio: '581 /313',
      // backgroundSize: 'contain'
    },
    [theme.breakpoints.down(400)]: {
      padding: '1rem 1.5rem'
    },
    '& .tmfBadge': {
      background: 'linear-gradient(180deg, #FDD98B 4.81%, #EAB647 38.46%, #846728 100%)',
      padding: '0.25rem 1.5rem',
      fontSize: '1rem',
      fontWeight: '700',
      textTransform: 'uppercase',
      textAlign: 'center',
      position: 'absolute',
      color: '#000',
      borderRadius: '10px 0 10px 0',
      top: '0',
      left: '0',
      [theme.breakpoints.down('md')]: {
        padding: '0.125rem 1.5rem',
        fontSize: '0.75rem'
      }
    },
    '& .tmf-plus-btn': {
      background: ' linear-gradient(180deg,#FDD98B 4.81%,#EAB647 38.46%, #846728 100%)',
      border: 'none',
      display: 'flex',
      justifyContent: 'center',
      fontWeight: '600',
      alignItems: 'center',
      gap: '0.5rem',
      '& img': {
        filter: 'brightness(0)',
        width: '12px'
      },
      '&:hover': {
        border: 'none',
        background: ' linear-gradient(180deg,#FDD98B 4.81%,#EAB647 38.46%, #846728 100%)',
        color: 'black'
      }
    },
    '& .vip-overlay': {
      position: 'absolute',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      background: 'rgb(255 255 255 / 11%)',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      zIndex: '2',
      backdropFilter: 'blur(8px)',
      alignItems: 'center',
      fontSize: '1rem',
      fontWeight: '700',
      gap: '10px',
      cursor: 'not-allowed',
      '& .vip-icon': {
        width: '5.5rem',
        height: '5.5rem',
        borderRadius: '10rem',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        background: '#00000099',
        backdropFilter: 'blur(25px)',
        [theme.breakpoints.down('md')]: {
          width: '3.875rem',
          height: '3.875rem'
        },
        '& img': {
          width: '2.25rem',
          [theme.breakpoints.down('md')]: {
            width: '1.25rem'
          }
        }
      }
    },
    '& h3': {
      fontSize: '1.875rem',
      fontWeight: '700',
      background: theme.colors.tournamentText,
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      whiteSpace: 'nowrap',
      maxWidth: '95%',
      textTransform: 'capitalize',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      [theme.breakpoints.down(1550)]: {
        fontSize: theme.spacing(1.5)
      },
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(1),
        minHeight: '18px'
      },
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(1.5),
        minHeight: '27px'
      },
      [theme.breakpoints.down(400)]: {
        fontSize: theme.spacing(0.75),
        minHeight: '12px'
      }
    },
    '& h4': {
      fontSize: '1.125rem',
      fontWeight: '700',
      color: theme.colors.textWhite,
      // maxWidth: '65%',
      '&.pool-prize': {
        color: theme.colors.YellowishOrange
      },
      [theme.breakpoints.down(1550)]: {
        fontSize: theme.spacing(0.75)
      },
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(0.875)
      },
      [theme.breakpoints.down(400)]: {
        fontSize: theme.spacing(0.75)
      }
    },
    '& .entry-fee': {
      fontSize: '1.25rem',
      fontWeight: '700',
      color: theme.colors.YellowishOrange,
      background: 'transparent',
      WebkitBackgroundClip: 'unset',
      WebkitTextFillColor: 'unset',
      [theme.breakpoints.down(1550)]: {
        fontSize: theme.spacing(0.875)
      },
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(0.75)
      },
      '& .original-price': {
        color: 'red',
        textDecoration: 'line-through',
        marginRight: '0.5rem'
      },
      '& .discounted-price': {
        color: '#00c853', // or any green
        fontWeight: 'bold'
      }
    },
    '& button': {
      marginTop: '1.25rem',
      fontSize: '1.25rem',
      fontWeight: '600',
      [theme.breakpoints.down(1550)]: {
        fontSize: theme.spacing(0.75),
        marginTop: '0.5rem',
        padding: '0.25rem 1rem !important'
      },
      [theme.breakpoints.down('md')]: {
        fontSize: theme.spacing(0.65),
        marginTop: '0.25rem'
      },
      [theme.breakpoints.down(400)]: {
        // fontSize: theme.spacing(0.65),
        marginTop: '0.25rem',
        padding: '0rem 1rem !important'
      }
    },
    '& .underline': {
      height: '1px',
      width: '50%',
      background: theme.colors.tournamentUnderline,
      marginTop: '0.5rem',
      marginBottom: '1rem',
      minHeight: '1px',
      [theme.breakpoints.down('md')]: {
        marginTop: '0.25rem',
        marginBottom: '0.25rem'
      }
    },
    '& .sc-badge': {
      position: 'relative',
      width: 'fit-content',
      marginLeft: '-0.5rem',
      '& span': {
        fontSize: '2.1875rem',
        fontWeight: '700',
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        whiteSpace: 'nowrap',
        color: theme.colors.textWhite,
        [theme.breakpoints.down(1550)]: {
          fontSize: '1rem'
        },
        [theme.breakpoints.down('md')]: {
          fontSize: '1rem'
        },
        [theme.breakpoints.down('sm')]: {
          fontSize: '0.75rem'
        }
      },
      '& img': {
        [theme.breakpoints.down(1550)]: {
          width: '120px'
        },
        [theme.breakpoints.down('md')]: {
          width: '120px'
        },
        [theme.breakpoints.down(420)]: {
          width: '90px',
          height: '25px'
        }
      }
    },
    '& .tournament-btn': {
      fontWeight: theme.typography.fontWeightBold,
      fontSize: theme.spacing(1.25),
      marginTop: theme.spacing(1),
      borderRadius: '3rem',
      border: '1px solid #ADADAD',
      background: 'transparent',
      padding: '3px 17px',
      minWidth: '15.25rem',
      maxHeight: '39px',
      color: '#ADADAD',
      [theme.breakpoints.down('md')]: {
        marginTop: theme.spacing(1)
      },
      [theme.breakpoints.down('sm')]: {
        minWidth: '10.25rem',
        fontSize: theme.spacing(1),
        marginTop: theme.spacing(0.5),
        fontWeight: '600',
        padding: '3px 6px !important'
      }
    },
    '& .tournament-btn-popup': {
      fontWeight: theme.typography.fontWeightBold,
      fontSize: theme.spacing(1.25),
      marginTop: theme.spacing(1),
      borderRadius: '3rem',
      border: '1px solid #fdb72e',
      background: '#fdb72e',
      padding: '3px 17px',
      maxHeight: '39px',
      color: '#000000',
      [theme.breakpoints.down('md')]: {
        marginTop: theme.spacing(1)
      },
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(0.7),
        marginTop: theme.spacing(0.5),
        fontWeight: '600',
        padding: '4px 22px !important'
      },
      '&:hover': {
        background: 'transparent',
        color: '#fdb72e',
        cursor: 'pointer',
        border: '1px solid #fdb72e'
      }
    },
    '& .preview': {
      '& .btn': {
        fontSize: theme.spacing(1),
        minWidth: theme.spacing(11.25),
        [theme.breakpoints.down('sm')]: {
          minWidth: 'auto',
          fontSize: theme.spacing(0.75)
        }
      }
    },
    '&.modal-card': {
      [theme.breakpoints.down('md')]: {
        aspectRatio: '232/100',
        backgroundSize: 'cover'
      },
      [theme.breakpoints.down('sm')]: {
        aspectRatio: '246/163',
        backgroundPosition: 'right'
      },
      '& .preview ': {
        minWidth: '243px',
        [theme.breakpoints.down('sm')]: {
          minWidth: '100px'
        },
        '& .btn': {
          fontSize: '20px',
          marginTop: '8px',
          width: '100%',
          maxHeight: '39px',
          [theme.breakpoints.down('sm')]: {
            minWidth: '10.25rem',
            fontSize: theme.spacing(1),
            marginTop: theme.spacing(0.5),
            fontWeight: '600',
            padding: '3px 6px !important'
            // marginTop: '0'
          }
        }
      },
      '& .sc-badge': {
        '& img': {
          width: '140px',
          [theme.breakpoints.down('sm')]: {
            width: '100px'
          }
        },
        '& span': {
          fontSize: '1.25rem',
          [theme.breakpoints.down('md')]: {
            fontSize: '1.125rem !important'
            // marginTop: '0'
          }
        }
      }
    }
  },
  switchTabWrap: {
    ...switchTabWrap(theme),
    '& .filter-wrap': {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing(1.75),
      marginBottom: theme.spacing(3.125),
      [theme.breakpoints.down('sm')]: {
        gap: theme.spacing(0.25)
      },
      '& .custom-tabs': {
        [theme.breakpoints.down('sm')]: {
          width: '100%'
        },
        '& .MuiTabs-root': {
          background: 'transparent',
          minHeight: 'auto',
          width: 'fit-content',
          border: `1px solid ${theme.colors.tabBorder}`,
          borderRadius: theme.spacing(2.18),
          [theme.breakpoints.down('md')]: {
            width: '100%',
            display: 'none'
          },
          '& .MuiTabs-scroller': {
            maxWidth: '100%',
            display: 'flex',
            whiteSpace: 'nowrap',
            flexWrap: 'nowrap',
            overflowX: 'auto',
            margin: theme.spacing(0, 0),

            '& .MuiTabs-indicator': {
              borderRadius: theme.spacing(4.1875),
              background: theme.colors.YellowishOrange,
              fontWeight: theme.typography.fontWeightExtraBold,
              color: theme.colors.textBlack,
              minHeight: theme.spacing(2.5),
              top: 0
            },

            '& .MuiTab-iconWrapper': {
              margin: '0',
              display: 'none'
            },

            '& button': {
              color: theme.colors.textWhite,
              position: 'relative',
              marginRight: theme.spacing(0.5),
              fontWeight: theme.typography.fontWeightExtraBold,
              cursor: 'pointer',
              fontSize: theme.spacing(1),
              transition: 'none',
              transform: 'none',
              zIndex: '1',
              padding: theme.spacing(0.313, 1),
              minHeight: theme.spacing(2.5),
              minWidth: theme.spacing(6),
              '&:last-child': {
                marginRight: '0'
              },

              '&:hover .MuiTab-iconWrapper': {
                // "& .image2": {
                // display: "block",
                // },
                // "& .image1": {
                // display: "none",
                // },
              },
              '& .MuiTab-iconWrapper': {
                '& .image2': {
                  display: 'none'
                },
                '& .image1': {
                  display: 'block'
                }
              },
              '&.Mui-selected': {
                color: theme.colors.textBlack,
                '& .MuiTab-iconWrapper': {
                  '& .image1': {
                    display: 'none'
                  },
                  '& .image2': {
                    display: 'block !important'
                  }
                }
              },

              '&:hover': {
                borderRadius: theme.spacing(4.1875),
                background: theme.colors.YellowishOrange,
                transition: 'none',
                transform: 'none',
                color: theme.colors.textBlack,
                border: '0'
              },
              '& img': {
                marginRight: theme.spacing(0.62),
                width: '25px',
                aspectRatio: '1'
              }
            }
          },
          '& .MuiTabScrollButton-root': {
            width: '1.25rem',
            '&.Mui-disabled': {
              opacity: '0.4 !important'
            }
          }
        }
      },
      '& .MuiInputBase-root': {
        borderRadius: '2rem',
        background: theme.colors.YellowishOrange,
        fontWeight: '600',
        border: 'none',
        [theme.breakpoints.up('md')]: {
          display: 'none'
        },
        '& .MuiSelect-select': {
          padding: '5px 32px 5px 16px'
        },
        '& svg': {
          fill: 'black'
        }
      },
      '& em': {
        color: 'black'
      }
    }
  },

  subscriberBanner: {
    margin: '0px auto 22px auto',
    border: '1px solid #FDB72E',
    borderRadius: '15px',
    padding: '12px 20px',
    marginBottom: '20px',
    fontSize: '24px',
    fontWeight: '700',
    display: 'flex',
    justifyContent: 'space-between',
    color: 'white',
    gap: '0.5rem',
    alignItems: 'center',

    [theme.breakpoints.down('md')]: {
      flexDirection: 'column',
      textAlign: 'center',
      fontSize: '16px'
    },

    '& .tmfPlus': {
      background: 'linear-gradient(180deg, #F7E041 30.56%, #FFA538 77.78%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      textShadow: '0px 3.85px 3.85px 0px #FDB72E59',
      margin: '0 4px'
    },

    '& .joinNow': {
      color: '#0094ff',
      fontWeight: 'bold',
      textDecoration: 'underline',
      marginLeft: '10px',
      whiteSpace: 'nowrap',
    },
  },

  NoData: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '500px',
    width: '100%',
    '& img': {
      height: '500px',
      width: '500px'
    }
  },

  claimBtn: {
    color: `${theme.colors.textBlack} !important`,
    fontSize: '1.5rem !important',
    minWidth: '120px !important',
    background: `${theme.colors.YellowishOrange} !important`,
    fontWeight: '700 !important',
    borderRadius: '0.5rem !important',
    lineHeight: '1.2 !important',
    display: 'flex',
    alignItems: 'center'
  },

  profileTabsDesign: {
    '& .ongoingTabs': {
      marginTop: '3.125rem',
      [theme.breakpoints.down('sm')]: {
        marginTop: '1.5rem'
      }
    }
  },

  sliderControler: {
    position: 'absolute',
    transform: 'translate(-50%, -50%)',
    left: '50%',
    top: '50%',
    width: '100%',
    zIndex: '5',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    '& .swiper-button-prev': {
      '&::after': {
        fontSize: `${theme.spacing(1)} !important`,
        fontWeight: '900',
        display: 'none'
      }
    },

    '& .swiper-button-next': {
      '&::after': {
        fontSize: `${theme.spacing(1)} !important`,
        fontWeight: '900',
        display: 'none'
      }
    }
  },
  mirrorText: {
    ...mirrorText(theme)
  },
  tournamentModal: {
    '& .MuiPaper-root': {
      background: theme.colors.modalGradient,
      borderRadius: `${theme.spacing(2.5)}!important`,
      padding: theme.spacing(0.325),
      minWidth: theme.spacing(46.375),
      maxWidth: theme.spacing(46.375),
      [theme.breakpoints.down('md')]: {
        minWidth: '98%'
      }
    },
    '& .MuiDialogContent-root': {
      background: `url(${otpBg})`,
      backgroundSize: 'cover',
      borderRadius: theme.spacing(2.5),
      textAlign: 'center',
      [theme.breakpoints.down('md')]: {
        padding: '1rem'
      },
      '& .close-btn': {
        position: 'absolute',
        top: theme.spacing(0.625),
        right: theme.spacing(0.625),
        color: theme.colors.textWhite,
        zIndex: 2
      },
      '& .modal-header': {
        position: 'relative',
        '& h4': {
          fontSize: theme.spacing(4.6875),
          background: theme.colors.tournamentModalText,
          WebkitBackgroundClip: 'text',
          textShadow: '0px 4px 4px #B5B5B540',
          WebkitTextFillColor: 'transparent',
          WebkitTextStrokeWidth: '1px',
          WebkitTextStrokeColor: '#F7E041',
          whiteSpace: 'nowrap',
          fontWeight: theme.typography.fontWeightBoldBlack,
          // marginBottom: theme.spacing(0.875),
          color: theme.colors.YellowishOrange,

          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(2.25)
          }
        },
        '& p': {
          color: theme.colors.textWhite,
          fontSize: theme.spacing(1.5625),
          fontWeight: theme.typography.fontWeightBold,
          textTransform: 'capitalize',
          lineHeight: theme.spacing(2),
          [theme.breakpoints.down('lg')]: {
            fontSize: theme.spacing(1),
            lineHeight: theme.spacing(1.1)
          }
        },
        '&.timer-header': {
          marginTop: theme.spacing(2)
        }
      },
      '& .modal-slider-wrap': {
        margin: theme.spacing(2, 0),
        '& .slider-card': {
          // maxWidth: theme.spacing(21.25),
          margin: '0 auto',
          borderRadius: theme.spacing(0.75),
          background: theme.colors.modalGameCard,
          overflow: 'hidden',
          '& .tournament-img': {
            height: theme.spacing(15),
            '& img': {
              width: '100%',
              height: '100%',
              objectFit: 'cover'
              // borderRadius: "100%",
            }
          },
          '& .modal-card-content': {
            padding: theme.spacing(2, 1),
            '& p': {
              color: theme.colors.textWhite,
              fontSize: theme.spacing(1),
              fontWeight: theme.typography.fontWeightBold,
              [theme.breakpoints.down('lg')]: {
                fontSize: theme.spacing(0.875)
              }
            },
            '& h6': {
              fontSize: theme.spacing(1.9375),
              color: theme.colors.textWhite,
              fontWeight: theme.typography.fontWeightBold,
              [theme.breakpoints.down('lg')]: {
                fontSize: theme.spacing(1.5)
              }
            },
            '& .pool-prize': {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: theme.colors.textWhite,
              fontWeight: theme.typography.fontWeightBold
            },
            '& .MuiButtonBase-root': {
              width: '100%',
              marginTop: theme.spacing(1),
              fontSize: theme.spacing(1.25)
            }
          }
        }
        // "& .swiper-slide": {
        // opacity: "0.3",
        // "&.swiper-slide-active": {
        // opacity: "1",
        // }
        // }
      }
    },
    '& .swiper-button-prev, & .swiper-button-next': {
      height: theme.spacing(1.75),
      width: theme.spacing(1.75),
      borderRadius: '100%',
      background: theme.colors.YellowishOrange,
      // [theme.breakpoints.down('md')]: {
      //   height: theme.spacing(2),
      //   width: theme.spacing(2)
      // },
      '&:after': {
        color: '#fff',
        fontSize: theme.spacing(0.75),
        fontWeight: theme.typography.fontWeightExtraBold,
        [theme.breakpoints.down('md')]: {
          fontSize: theme.spacing(0.875)
        }
      },
      '&.swiper-button-disabled': {
        background: '#3A3B3D',
        opacity: '1 !important'
      }
    },
    '& .swiper-button-prev': {
      [theme.breakpoints.down('sm')]: {
        left: '0'
      }
    },
    ' & .swiper-button-next': {
      [theme.breakpoints.down('sm')]: {
        right: '0'
      }
    },
    '& .btn-wrap': {
      '& .btn-primary': {
        borderColor: theme.colors.modalBtnColor,
        color: theme.colors.modalBtnColor,
        background: 'transparent',
        '&:hover': {
          background: theme.colors.YellowishOrange,
          borderColor: theme.colors.YellowishOrange,
          color: theme.colors.textBlack
        }
      }
    },
    '& .timer-modal-wrap': {
      padding: theme.spacing(2, 0),
      '& .timer-card': {
        background: `url(${counterBg})`,
        backgroundPosition: 'center',
        backgroundSize: 'cover',
        minHeight: theme.spacing(10.5),
        maxWidth: theme.spacing(22.125),
        margin: '0 auto',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        flexDirection: 'column',
        [theme.breakpoints.down('sm')]: {
          backgroundSize: '100% 100%'
        },
        gap: theme.spacing(0.625),
        '& .end-tag': {
          background: theme.colors.YellowishOrange,
          minWidth: theme.spacing(9.375),
          padding: theme.spacing(0.125, 1),
          borderRadius: theme.spacing(0.5625),
          fontSize: theme.spacing(1.875),
          fontWeight: theme.typography.fontWeightBold,
          position: 'absolute',
          top: theme.spacing(-0.625),
          textTransform: 'uppercase',
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(1.2),
            minWidth: 'auto'
          }
        },
        '& h4': {
          fontSize: theme.spacing(5),
          lineHeight: theme.spacing(3.125),
          color: theme.colors.YellowishOrange,
          marginTop: theme.spacing(1.875),
          fontWeight: theme.typography.fontWeightBold,
          [theme.breakpoints.down('sm')]: {
            fontSize: theme.spacing(3)
          }
        },
        '& p': {
          fontSize: theme.spacing(0.8125),
          fontWeight: theme.typography.fontWeightMedium,
          color: theme.colors.textWhite
        }
      },
      '& .btn-wrap': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        gap: theme.spacing(1),
        margin: theme.spacing(2, 0),
        '& .btn': {
          minWidth: theme.spacing(18.75),
          fontSize: theme.spacing(1.25),
          [theme.breakpoints.down('md')]: {
            minWidth: 'auto',
            fontSize: theme.spacing(1)
          }
        }
      }
    },
    '& .swiper-wrapper': {
      '& .swiper-slide': {
        padding: '0 50px',
        [theme.breakpoints.down('md')]: {
          padding: '0'
        }
      }
    }
  }
}))
