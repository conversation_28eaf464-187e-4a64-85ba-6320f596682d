import React, { useState, useEffect, useRef, lazy, Suspense } from 'react'
import BrandLogo from '../../../components/ui-kit/icons/brand/brand-logo.webp'
import { usePortalStore } from '../../../store/userPortalSlice'
import useStyles from './style'
import { Button, Grid } from '@mui/material'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import CloseIcon from '@mui/icons-material/Close'
import menuIcon from '../../../components/ui-kit/icons/webp/menus.webp'
import { useSiteLogoStore } from '../../../store/store'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import HeaderRibbon from '../../NotFound/HeaderRibbon'
import LazyImage from '../../../utils/lazyImage'
import { getVipRoute } from '../../../utils/cookiesCollection'

// Lazy-loaded modals
const Signin = lazy(() => import('../../../components/Modal/Signin'))
const Signup = lazy(() => import('../../../components/Modal/Signup'))

const sectionIds = ['how-it-works', 'about', 'hot-games', 'reviews', 'faq', 'blog']

const LandingHeader = () => {
  const classes = useStyles()
  const openPortal = usePortalStore((state) => state.openPortal)
  const logoData = useSiteLogoStore((state) => state)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [selectedSection, setSelectedSection] = useState('')
  const sectionRefs = useRef({})
  const navigate = useNavigate()

  const toggleMenu = () => setIsMenuOpen((prev) => !prev)

  const scrollToSection = (e, id) => {
    e.preventDefault()
    const section = sectionRefs.current[id]
    if (section) {
      const yOffset = -100
      const yPosition = section.getBoundingClientRect().top + window.scrollY + yOffset
      window.scrollTo({ top: yPosition, behavior: 'smooth' })
    }
  }

  const handleLinkClick = (e, id) => {
    navigate('/online-social-casino-games', { state: { sectionId: id } })
    scrollToSection(e, id)
    toggleMenu()
  }

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) setSelectedSection(entry.target.id)
        })
      },
      { root: null, threshold: 0.3 }
    )

    sectionIds.forEach((id) => {
      const section = document.getElementById(id)
      if (section) {
        observer.observe(section)
        sectionRefs.current[id] = section
      }
    })

    return () => observer.disconnect()
  }, [])

  const location = useLocation()
  const scrollToSec = location.state?.scrollToSec
  const vipRoute = getVipRoute('vipRoute')

  useEffect(() => {
    if (scrollToSec) {
      const section = document.getElementById(scrollToSec)
      if (section) {
        setSelectedSection(scrollToSec)
        section.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }, [scrollToSec])

  useEffect(() => {
    if (vipRoute) {
      handleLogin()
    }
  }, [vipRoute])

  const handleLogin = () =>
    openPortal(
      () => (
        <Suspense fallback={<div>Loading...</div>}>
          <Signin />
        </Suspense>
      ),
      'loginModal'
    )

  const handleJoinNow = () =>
    openPortal(
      () => (
        <Suspense fallback={<div>Loading...</div>}>
          <Signup />
        </Suspense>
      ),
      'signupModal'
    )

  const handleWithScroll = (section) => {
    navigate('/online-social-casino-games', { state: { sectionId: section } })
  }

  return (
    <Grid className={classes.landingHeader}>
      <HeaderRibbon />
      <Grid className='landing-header-content'>
        <Link to='/'>
          <img
            src={logoData?.desktopLogo || BrandLogo}
            alt='Logo'
            className='brand-logo'
            loading='eager'
            fetchPriority='high'
            decoding='async'
            width='110'
            height='63'
            sizes='(max-width: 768px) 100px, 110px'
          />
        </Link>
        <Grid className='header-menu-wrap'>
          <button
            onClick={() => handleWithScroll('how-it-works')}
            className={selectedSection === 'how-it-works' ? 'active' : ''}
          >
            How It Works
          </button>
          <Link to='/about-us' className={selectedSection === 'about' ? 'active' : ''}>
            About
          </Link>
          <Link to='/games' className={selectedSection === 'hot-games' ? 'active' : ''}>
            Games
          </Link>
          <button onClick={() => handleWithScroll('reviews')} className={selectedSection === 'reviews' ? 'active' : ''}>
            Reviews
          </button>
          <Link to='/faq' className={selectedSection === 'faq' ? 'active' : ''}>
            FAQ
          </Link>
          <Link to='/blog' className={selectedSection === 'blog' ? 'active' : ''}>
            Blog
          </Link>
        </Grid>

        <Grid className='mob-right-group'>
          <Grid className='header-right'>
            <Button className='btn btn-secondary' onClick={handleLogin} aria-label='Login'>
              <span className='btn-content'>
                <ArrowCircleRightOutlinedIcon />
                Login
              </span>
              <span className='mob-text'>Login</span>
            </Button>
            <Button className='btn btn-primary' onClick={handleJoinNow} aria-label='SignUp'>
              <span className='btn-content'>
                <ArrowCircleRightOutlinedIcon />
                Sign Up
              </span>
              <span className='mob-text'>Sign Up</span>
            </Button>
          </Grid>

          <Button
            className={`mobile-toggle-menu ${isMenuOpen ? 'btn-active' : ''}`}
            onClick={toggleMenu}
            aria-label='Toggle menu'
          >
            {/* <img loading='lazy' src={menuIcon} className='menu-icon' alt='Menu' /> */}
            <LazyImage src={menuIcon} alt='Menu' className='menu-icon' lazy={false} />
            <CloseIcon className='cross-icon' />
          </Button>
        </Grid>

        <Grid className={`landing-mob-menu ${isMenuOpen ? 'mob-menu-active' : ''}`}>
          <Grid className='menu-wrap'>
            <button onClick={(e) => handleLinkClick(e, 'how-it-works')}>How It Works</button>
            <Link to='/about-us'>About</Link>
            <Link to='/games'>Games</Link>
            <button onClick={(e) => handleLinkClick(e, 'reviews')}>Reviews</button>
            <Link to='/faq'>FAQ</Link>
            <Link to='/blog'>Blog</Link>
            <Grid className='mob-sidebar-btn'>
              <Button className='btn btn-secondary' onClick={handleLogin} data-tracking='Home.Header.Login.Btn'>
                <ArrowCircleRightOutlinedIcon />
                Login
              </Button>
              <Button className='btn btn-primary' onClick={handleJoinNow} data-tracking='Home.Header.JoinNow.Btn'>
                <ArrowCircleRightOutlinedIcon />
                SignUp
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default LandingHeader
