import React, { useState, useEffect, useCallback } from 'react'
import useStyles from '../../Lobby/Lobby.styles'
import '../../../../src/App.css'
import { Grid, Typography, CircularProgress, Tooltip } from '@mui/material'
import { toast } from 'react-hot-toast'
import whitePlay from '../../../components/ui-kit/icons/svg/white-play-button.svg'
import HeartFill from '../../../components/ui-kit/icons/utils/heartFill.webp'
import CasinoCard from '../../../components/ui-kit/icons/utils/casinoGames.webp'
import Heart from '../../../components/ui-kit/icons/utils/heart.webp'
import TournamentLogo from '../../../components/ui-kit/icons/png/tournament-logo.png'
import PragmaticJackpotSCLogo from '../../../components/ui-kit/icons/svg/pragmatic-sc.svg'
import PragmaticJackpotGCLogo from '../../../components/ui-kit/icons/svg/pragmatic-gc.svg'
import { useFavToggleMutation } from '../../../reactQuery/casinoQuery'
import { getItem, getLoginToken } from '../../../utils/storageUtils'
import { useUserStore } from '../../../store/useUserSlice'
import { useLocation } from 'react-router-dom'
import { updateSDKPageVisit } from '../../../utils/optimoveHelper'
import ScrollToTop from '../../../components/ScrollToTop'
import {
  usePortalStore,
  useSelectedProviderStore,
  useFavoriteGamesStore,
  useGamesStore
} from '../../../store/store'
import { SubCategoryConstants } from '../../../components/SideBar/constants'
import MobileVerification from '../../MobileVerification'
import { CasinoQuery, useGetProfileMutation } from '../../../reactQuery'
import { dynamicMerge, formatPriceWithCommas } from '../../../utils/helpers'
import LazyImage from '../../../utils/lazyImage'
import { usePragmaticJackpotStore } from '../../../store/usePragmaticJackpot'

const GamesList = ({ handlePlayNow, handleLoadMore }) => {
  const classes = useStyles()
  const location = useLocation()
  const currentUrl = window.location.origin + location.pathname + location.search
  const coinType = getItem('coin')
  const portalStore = usePortalStore((state) => state)
  if (import.meta.env.VITE_NODE_ENV === 'production') {
    updateSDKPageVisit(currentUrl, 'Game_Lists')
  }
  const auth = useUserStore((state) => state)
  const [favorites, setFavorites] = useState({})
  const selectedProviderStore = useSelectedProviderStore((state) => state)
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const [gameId, setGameId] = useState(null)
  const [name, setName] = useState('')
  const [isRefreshingFavorites, setIsRefreshingFavorites] = useState(false)
  const { updateFavorites } = useFavoriteGamesStore((state) => state)
  const { pragmaticJackpotSc, pragmaticJackpotGc } = usePragmaticJackpotStore()
  // gamesStore
  const {
    setSubCategories,
    gameData: data,
    gameDataCount,
    limit,
    pageNo,
    setPageNo,
    gameDataLoading,
    setGameDataLoading,
    subCategoryName,
    isLoading,
    setGameData,
    setGameDataCount,
    setFavoriteIds
  } = useGamesStore((state) => state)

  const selectedSubCat = useGamesStore((state) => state.selectedSubCat)

  const mutationFavToggle = useFavToggleMutation({
    onSuccess: (res, variables) => {
      const newFavoriteState = !favorites[variables.gameId]

      setFavorites((prevState) => ({
        ...prevState,
        [variables.gameId]: newFavoriteState // Update favorite status for the toggled game
      }))

      setFavoriteIds((prevState) => ({
        ...prevState,
        [variables.gameId]: newFavoriteState // Keep favoriteIds in sync
      }))

      updateFavorites(variables.gameId)

      if (selectedProviderStore.selectedProviderId !== '') {
        subcategoryListMutation.mutate({ masterCasinoProviderId: selectedProviderStore.selectedProviderId })
      } else {
        subcategoryListMutation.mutate()
      }
      if (selectedSubCat === SubCategoryConstants.FAVORITE_GAMES) {
        // When toggling favorites, refresh the list and reset pagination
        setIsRefreshingFavorites(true)
        setPageNo(1)
        const filterData = { limit: limit, page: 1 }
        favGameListMutation.mutate(filterData)
      }
      toast.success(variables?.request ? 'Added to Favorites' : 'Removed from Favorites')
    },
    onError: (error) => {
      if (error?.response?.data?.errors.length > 0) {
        const { errors } = error.response.data
        errors.forEach((error) => {
          console.log(error)
        })
      }
    }
  })

  const toggleFavorite = (gameId, isFav) => {
    mutationFavToggle.mutate({ request: !isFav, gameId })
  }

  const favSuccessToggler = (data1) => {
    const newData = data1?.data?.data?.rows
    if (isRefreshingFavorites || pageNo === 1) {
      // Replace data when refreshing after favorite toggle or on first page
      setGameData(newData)
      setIsRefreshingFavorites(false)
    } else {
      // Append data for pagination
      const updatedGameData = dynamicMerge(data || [], newData || [], 'masterCasinoGameId')
      setGameData(updatedGameData)
    }
    setGameDataCount(data1?.data?.data?.count)
    setGameDataLoading(false)
  }

  const favErrorToggler = (error) => {
    console.log(error)
    setGameDataLoading(false)
  }

  const favGameListMutation = CasinoQuery.useFavGameListMutation({
    successToggler: favSuccessToggler,
    errorToggler: favErrorToggler
  })

  /* success response of subcategoryListMutation */
  const successToggler = (data) => {
    setSubCategories(data?.data?.data)
  }

  /* error response of subcategoryListMutation */
  const errorToggler = (error) => {
    setGameDataLoading(false)
    console.log(error)
  }

  /* subcategoryListMutation api hit  */
  const subcategoryListMutation = CasinoQuery.useSubcategoryListMutation({
    successToggler,
    errorToggler
  })

  useEffect(() => {
    const newFavorites = {}
    data?.forEach((game) => {
      if (game.FavoriteGames) {
        newFavorites[game.masterCasinoGameId?.toString()] = game.FavoriteGames
        updateFavorites(game.masterCasinoGameId)
      }
    })

    // Merge with existing favorites instead of replacing them
    setFavorites(prevFavorites => ({
      ...prevFavorites,
      ...newFavorites
    }))
    setFavoriteIds(prevFavoriteIds => ({
      ...prevFavoriteIds,
      ...newFavorites
    }))
  }, [data])

  // Infinite scroll logic
  const handleScroll = useCallback(() => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollHeight = document.documentElement.scrollHeight
    const clientHeight = document.documentElement.clientHeight

    // Trigger load more when user is 200px from bottom
    if (scrollTop + clientHeight >= scrollHeight - 200) {
      if (Math.ceil(gameDataCount / limit) > pageNo && !gameDataLoading) {
        handleLoadMore()
        setGameDataLoading(true)
      }
    }
  }, [gameDataCount, limit, pageNo, gameDataLoading, handleLoadMore, setGameDataLoading])

  useEffect(() => {
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [handleScroll])
  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (!res?.data?.data?.phoneVerified) {
        portalStore.openPortal(
          () => (
            <MobileVerification
              calledFor='gamePlay'
              handlePlayNow={() => handlePlayNow(gameId, name, subCategoryName)}
            />
          ),
          'innerModal'
        )
      }
    },
    onError: (error) => {
      console.log('**************error', error)
    }
  })
  const handlePhoneVerification = (gameId, name, subCategoryName) => {
    if (coinType === 'SC' && !auth?.userDetails?.phoneVerified && (!!getLoginToken() || auth.isAuthenticate)) {
      setGameId(gameId)
      setName(name)
      getProfileMutation.mutate()
    } else {
      handlePlayNow(gameId, name, subCategoryName)
    }
  }

  return (
    <>
      <ScrollToTop />
      <>
        <Grid className='games-grid' initial='hidden' animate='visible'>
          <>
            <Grid className={classes.subcategoryListWrap} key='categoryGames_001'>
              {data?.map((game, idx) => {
                const gameId = String(game?.masterCasinoGameId)

                const isScPragmatic = coinType === 'SC' && pragmaticJackpotSc?.hasOwnProperty(gameId)
                const isGcPragmatic = coinType === 'GC' && pragmaticJackpotGc?.hasOwnProperty(gameId)
                const jackpotValue = isScPragmatic
                  ? pragmaticJackpotSc?.[gameId]
                  : isGcPragmatic
                    ? pragmaticJackpotGc?.[gameId]
                    : null

                return (
                  <div
                    className='custom-col-2'
                    key={`${game.masterCasinoGameId}_${idx}`}
                  >
                    <Grid key={`${game.masterCasinoGameId}_1_${idx}`}>
                      <Grid>
                        <Tooltip
                          title={game?.gameInTournament ? 'TOURNAMENT' : ''}
                          arrow
                          disableHoverListener={!game?.gameInTournament}
                          placement='top-start' // You can adjust this if needed
                          componentsProps={{
                            tooltip: {
                              sx: {
                                backgroundColor: '#FF3000 ',
                                color: 'white',
                                textAlign: 'center',
                                fontWeight: '800',
                                fontSize: '14px'
                              },
                              style: {
                                maxWidth: 'none' // optional: ensures full text is visible
                              }
                            },
                            arrow: {
                              sx: {
                                color: '#FF3000',
                                position: 'absolute',
                                left: '16px !important',
                                transform: 'translate(0px, 0px) !important',
                                marginTop: '-8px !important'
                              }
                            }
                          }}
                        >
                          <Grid className='casino-card'>
                            {(!!getLoginToken() || auth.isAuthenticate) && (
                              <Grid className='fav-icon'>
                                <span
                                  onClick={
                                    !isLoading
                                      ? () =>
                                          toggleFavorite(
                                            game.masterCasinoGameId,
                                            favorites[game.masterCasinoGameId?.toString()]
                                          )
                                      : undefined
                                  }
                                >
                                  <LazyImage
                                    alt='heart'
                                    src={favorites[game.masterCasinoGameId?.toString()] ? HeartFill : Heart}
                                    width='100%'
                                    height='100%'
                                  />
                                </span>
                              </Grid>
                            )}
                            <Grid className='casino-card'>
                              <LazyImage
                                src={game?.imageUrl || CasinoCard}
                                alt='Casino'
                                className='casinoGame-img'
                              // loading='lazy'
                              />
                              <Grid
                                className='casino-overlay'
                                onClick={() => {
                                  handlePhoneVerification(game?.masterCasinoGameId, game?.name, subCategoryName)
                                }}
                              >
                                <Typography
                                  variant='h6'
                                  sx={{ lineHeight: '20px', textAlign: 'center', padding: '0 10px' }}
                                >
                                  <b>{game.name}</b>
                                </Typography>

                                <LazyImage src={whitePlay} alt='Play' className={classes.playImg} />

                                <b>Play Now </b>
                              </Grid>
                              {jackpotValue !== null && (
                                <div
                                  className='prgamatic-jackpot-amount-wrapper'
                                >
                                  <LazyImage
                                    src={coinType === 'SC' ? PragmaticJackpotSCLogo : PragmaticJackpotGCLogo}
                                    alt='prgamatic-jakcpot-logo'
                                  />
                                  <Typography
                                    style={{
                                      color: ` ${coinType === 'SC' ? '#00C80E' : '#FDB72E'}`,
                                      fontWeight: '700',
                                      fontSize: '10px'
                                    }}
                                  >
                                    {formatPriceWithCommas(jackpotValue)} {coinType}
                                  </Typography>
                                </div>
                              )}
                              {game?.gameInTournament && (
                                <LazyImage src={TournamentLogo} alt='tournament-logo' className='tournamentLogo' />
                              )}
                            </Grid>
                          </Grid>
                        </Tooltip>
                      </Grid>
                    </Grid>
                  </div>
                )
              })}
            </Grid>

            {(data?.length === 0 || gameDataCount === 0) && (
              <Grid className='no-data-content'>
                <Typography sx={{ textAlign: 'center', color: 'red' }}> No Games Found</Typography>
              </Grid>
            )}
          </>
        </Grid>
        {gameDataLoading === true && (
          <Grid className={classes.loadMore}>
            <CircularProgress size={25} />
          </Grid>
        )}
      </>
    </>
  )
}

export default GamesList
