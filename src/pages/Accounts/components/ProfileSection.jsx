import React, { useEffect, useState } from 'react'
import useStyles from '../Accounts.styles'
import '../../../../src/App.css'
import { Button, Box, Grid, Typography, CircularProgress, Tooltip } from '@mui/material'
import usePersonalInfo from '../hooks/useProfile'
import { useUserStore } from '../../../store/useUserSlice'
import uploadImg from '../../../components/ui-kit/icons/svg/upload-photo.svg'
import MobileVerification from '../../MobileVerification'
import { usePortalStore } from '../../../store/userPortalSlice'
import BrandLogoMob from '../../../components/ui-kit/icons/brand/brand-logo.webp'
import { styled } from '@mui/material/styles'
import { isValidPhoto } from '../../../utils/helpers'
import toast from 'react-hot-toast'
import CustomDatePicker from '../../../components/CustomDatePicker'
import Select from 'react-select'
import { Controller } from 'react-hook-form'
/* eslint-disable multiline-ternary */

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1
})
const ProfileSection = () => {
  const classes = useStyles()
  const {
    handleSubmit,
    register,
    selectedState,
    selectedGender,
    isSubmitFormLoading,
    errors,
    selectedDate,
    genderOptions,
    stateList: stateData,
    setValue,
    userDetails,
    setSelectedState,
    handleOnFormSubmit,
    userProfileImage,
    setSelectedDate,
    control
  } = usePersonalInfo()
  // const autocompleteInput = useRef(null)

  const [formError, setFormError] = useState('')
  const user = useUserStore((state) => state)
  const portalStore = usePortalStore((state) => state)
  const btnDisabled = userDetails?.kycStatus === 'K4' || userDetails?.kycStatus === 'K5' ? true : false
  const handleProfileImage = (e) => {
    const profileImage = e.target.files[0]
    if (profileImage && isValidPhoto(profileImage.name)) {
      userProfileImage(profileImage)
    } else {
      toast.error('File type not supported')
    }
  }

  const handleMobileVerificationOpen = () => {
    portalStore.openPortal(() => <MobileVerification />, 'innerModal')
  }

  const isFieldDisabled = (field) => {
    const kycStatus = userDetails?.kycStatus

    if (field === 'dateOfBirth' && userDetails?.dateOfBirth === '1970-01-01') {
      return false // Editable
    }
    if (kycStatus === 'K4' || kycStatus === 'K5') {
      return true
    }

    return false // All fields are editable if KYC status is not K4 or K5
  }

  let autoComplete
  let address1Field
  let postalField

  useEffect(() => {
    try {
      if (window.google && window.google.maps) {
        address1Field = document.querySelector('#ship-address')
        postalField = document.querySelector('#postcode')
        autoComplete = new window.google.maps.places.Autocomplete(address1Field, {
          componentRestrictions: { country: ['us'] },
          fields: ['address_components', 'geometry'],
          types: ['address']
        })
        autoComplete.addListener('place_changed', fillInAddress)
      }
    } catch (error) {
      console.error('GOOGLE MAPS ERROR in initializeGoogleMapsAPI:', error)
    }
  }, [])

  const fillInAddress = () => {
    const place = autoComplete.getPlace()
    let address1 = ''
    let postcode = ''
    let city = ''
    let stateCode = ''
    for (const component of place.address_components) {
      const componentType = component.types[0]

      switch (componentType) {
        case 'street_number': {
          address1 = `${component.long_name} ${address1}`
          break
        }

        case 'route': {
          address1 += component.short_name
          break
        }

        case 'postal_code': {
          postcode = `${component.long_name}${postcode}`
          break
        }

        case 'postal_code_suffix': {
          postcode = `${postcode}-${component.long_name}`
          break
        }
        case 'locality':
          city = component.long_name
          document.querySelector('#locality').value = component.long_name
          break
        case 'administrative_area_level_1': {
          stateCode = component.short_name
          document.querySelector('#state').value = component.short_name
          break
        }
      }
    }
    address1Field.value = address1
    postalField.value = postcode
    setValue('addressLine_1', address1, { shouldValidate: true })
    setValue('city', city, { shouldValidate: true })
    setValue('state', stateCode, { shouldValidate: true })
    setValue('zipCode', postcode, { shouldValidate: true })
    setSelectedState(stateCode)
  }

  const handleDateValidation = (isValid, message, date) => {
    setValue('dateOfBirth', date)
    setSelectedDate(date)
    if (!isValid) {
      setFormError('You must be at least 18 years old.') // Set error if the selected date is not valid
    } else {
      setFormError('') // Clear error if the selected date is valid
    }
  }

  const handleFormSubmit = (formData) => {
    if (!selectedDate) {
      setFormError('Date of birth is required')
      return
    }

    handleOnFormSubmit(formData, selectedDate)
  }

  const stateOptions = stateData?.map((state) => ({
    value: state.stateCode,
    label: state.name
  }))

  return (
    <Box className='profile-section-detail'>
      <form onSubmit={handleSubmit(handleFormSubmit)} id='address-form' action='' method='get' autoComplete='off'>
        <Box className='profile'>
          <Grid container spacing={2}>
            <Grid
              item
              xs={12}
              sm={12}
              md={12}
              display='flex'
              alignItems={'center'}
              paddingTop={'2rem'}
              flexDirection={{ xs: 'column', sm: 'row' }}
              gap={{ xs: 1, md: 4 }}
            >
              <Grid className={classes.userImgWrap}>
                <Grid className={classes.userImg} style={{ display: 'flex' }}>
                  <img src={userDetails?.profileImage || BrandLogoMob} alt='User' />
                </Grid>
                <Tooltip title='Supported file type only jpg png webp'>
                  <Button component='label' variant='contained' className={classes.uploadImgBtn}>
                    <img src={uploadImg} alt='User-img-btn' />
                    <VisuallyHiddenInput type='file' onChange={handleProfileImage} />
                  </Button>
                </Tooltip>
              </Grid>

              <Grid className={classes.userKyc}>
                <Grid className={classes.userDetailsRight}>
                  <Typography variant='h4'>
                    {user?.userDetails?.firstName && user?.userDetails?.lastName
                      ? `${user?.userDetails?.firstName} ${user?.userDetails?.lastName}`
                      : user?.userDetails?.username || 'Player'}
                  </Typography>

                  {user?.userDetails?.email && (
                    <Typography>
                      {`Email : ${user?.userDetails?.email}`}
                      {/* <Link href='#' onClick={handleMobileVerificationOpen} style={{ paddingLeft: "10px" }}>
                      <img src={edit} alt='Edit' />
                    </Link> */}
                    </Typography>
                  )}
                  {user?.userDetails?.phoneVerified ? (
                    <Typography>{`Mobile : +${user?.userDetails?.phoneCode} ${user?.userDetails?.phone}`}</Typography>
                  ) : (
                    <Button
                      variant='contained'
                      className='saveBtn'
                      style={{ marginTop: '15px' }}
                      onClick={handleMobileVerificationOpen}
                    >
                      <span className='btn-span'>Verify Your Phone Number</span>
                    </Button>
                  )}
                </Grid>
              </Grid>

            </Grid>
          </Grid>

          <Grid container spacing={2} className={classes.inputDetail}>
            <Grid item xs={12} sm={6} md={4}>
              <input
                id='outlined-basic'
                disabled={isFieldDisabled('firstName')}
                label=''
                className='inputSelect'
                variant='outlined'
                placeholder='First Name *'
                {...register('firstName')}
              />

              {errors?.firstName && <p className={classes.inputError}> {errors?.firstName?.message}</p>}

              {/* </Grid> */}
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <input
                id='outlined-basic'
                label=''
                disabled={isFieldDisabled('middleName')}
                variant='outlined'
                className='inputSelect'
                placeholder='Middle Name'
                {...register('middleName')}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <input
                id='outlined-basic'
                label=''
                className='inputSelect'
                variant='outlined'
                placeholder='Last Name *'
                disabled={isFieldDisabled('lastName')}
                {...register('lastName')}
              />
              {errors?.lastName && <p className={classes.inputError}>{errors?.lastName?.message} </p>}
            </Grid>

            {user?.userDetails?.email && (
              <Grid item xs={12} sm={6} md={4}>
                <input
                  disabled
                  id='email_address'
                  className='inputSelect'
                  label=''
                  variant='outlined'
                  placeholder='Email Address'
                  {...register('email')}
                />
              </Grid>
            )}
            <Grid item xs={12} sm={6} md={4}>
              {isFieldDisabled('gender') ? (
                <div className='inputSelect disabled' style={{ padding: '8px 12px' }}>
                  {selectedGender
                    ? genderOptions.find((option) => option.value === selectedGender)?.label
                    : 'Select a gender'}
                </div>
              ) : (
                <Controller
                  control={control} // get this from useForm
                  name='gender'
                  render={({ field }) => (
                    <Select
                      id='gender-select'
                      options={genderOptions}
                      placeholder='Select a gender'
                      value={genderOptions.find((opt) => opt.value === field.value)}
                      onChange={(selectedOption) => field.onChange(selectedOption?.value)}
                      classNamePrefix='react-select'
                      isDisabled={isFieldDisabled('gender')}
                      isClearable
                    />
                  )}
                />
              )}
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <CustomDatePicker
                setDate={userDetails?.dateOfBirth}
                disabled={isFieldDisabled('dateOfBirth')}
                onDateValidation={handleDateValidation}
              />
              {formError !== '' && <p className={classes.inputError}> {formError}</p>}
              {errors?.dateOfBirth && <p className={classes.inputError}> {errors?.dateOfBirth?.message}</p>}
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <input
                id='ship-address'
                label=''
                {...register('addressLine_1')}
                className='inputSelect'
                placeholder={'Address Line *'}
                // ref={autocompleteInput}
                autocomplete='off'
                disabled={isFieldDisabled('addressLine_1')}
              />
              {errors?.addressLine_1 && <p className={classes.inputError}> {errors?.addressLine_1?.message}</p>}
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <input
                id='address2'
                className='inputSelect'
                label=''
                variant='outlined'
                placeholder='Apartment, unit, suite, or floor #'
                disabled={isFieldDisabled('addressLine_2')}
                {...register('addressLine_2')}
              />
              {errors?.addressLine_2 && <p className={classes.inputError}> {errors?.addressLine_2?.message}</p>}
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <input
                id='postcode'
                type='text'
                className='inputSelect'
                label=''
                variant='outlined'
                placeholder='Postal code *'
                disabled={isFieldDisabled('zipCode')}
                {...register('zipCode')}
              />
              {errors?.zipCode && <p className={classes.inputError}>{errors?.zipCode?.message}</p>}
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <input
                id='locality'
                className='inputSelect'
                label=''
                variant='outlined'
                placeholder='City *'
                disabled={isFieldDisabled('city')}
                {...register('city')}
              />
              {errors?.city && <p className={classes.inputError}>{errors?.city?.message}</p>}
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              {/* <input
                  id='state'
                  className='inputSelect'
                  label=''
                  variant='outlined'
                  placeholder='State (Enter state code)'
                  disabled={isFieldDisabled('state')}
                  {...register('state')}
                /> */}
              {isFieldDisabled('state') ? (
                <div className='inputSelect disabled' style={{ padding: '8px 12px' }}>
                  {selectedState
                    ? stateData?.find((state) => state.stateCode === selectedState)?.name
                    : 'Select a State *'}
                </div>
              ) : (
                <Controller
                  control={control} // from useForm()
                  name='state'
                  render={({ field }) => (
                    <Select
                      id='state'
                      options={stateOptions}
                      placeholder='Select a State *'
                      value={stateOptions.find((opt) => opt.value === field.value)}
                      onChange={(selectedOption) => field.onChange(selectedOption?.value)}
                      classNamePrefix='react-select'
                      isDisabled={isFieldDisabled('state')}
                      isClearable
                    />
                  )}
                />
              )}
              {selectedState === '' && <p className={classes.inputError}>{errors?.state?.message}</p>}
            </Grid>
          </Grid>
        </Box>
        <Grid className={classes.profileBottom}>
          <Button variant='contained' className='saveBtn' type='submit' disabled={isSubmitFormLoading || btnDisabled}>
            {isSubmitFormLoading ? (
              <CircularProgress size={24} style={{ marginRight: 8 }} />
            ) : (
              <span className='btn-span'>Save</span>
            )}
          </Button>
        </Grid>
      </form>
    </Box>
  )
}

export default ProfileSection
