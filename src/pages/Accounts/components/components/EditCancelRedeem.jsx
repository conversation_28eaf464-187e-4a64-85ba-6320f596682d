import React, { useEffect, useState } from 'react'
import useStyles from '../CancleRedeemModal.styles'
import { Button, FormLabel, Grid, IconButton, Typography, OutlinedInput, Tooltip } from '@mui/material'
import Select from 'react-select'
import { usePortalStore } from '../../../../store/userPortalSlice'
import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import { redeemCancelSchema } from '../schema'

import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import { useGetPayByBankData, useGetTrustlyData } from '../../hooks/usePayByBankData'
/* eslint-disable multiline-ternary */

const EditCancelRedeem = ({ confirmRedeemRequest, item, setShowEditRedeemData, type = 'transaction' }) => {
  const isActionableEmail = item?.redeememail?.includes('@') || item?.actionableEmail?.includes('@')
  const portalStore = usePortalStore((state) => state)
  const [isLoading, setIsLoading] = useState(false)
  const [isDisable, setIsDisable] = useState(false)
  const [bankAccountOptions, setBankAccountOptions] = useState([])
  const [selectedBank, setSelectedBank] = useState()
  const handleClose = () => {
    portalStore.closePortal()
  }
  let email = item?.redeememail || item?.actionableEmail
  const {
    handleSubmit,
    register,
    formState: { errors },
    watch,
    setValue
  } = useForm({
    resolver: yupResolver(redeemCancelSchema(isActionableEmail)),
    defaultValues: {
      amount: item.amount,
      actionableEmail: email,
      bank: email
    }
  })

  const paymentProvider = item?.paymentProvider || item?.method

  const shouldFetchBankData = paymentProvider === 'PAY_BY_BANK'

  const shouldFetchTrustlyData = paymentProvider === 'TRUSTLY'

  const { payByBankData = {} } = shouldFetchBankData ? useGetPayByBankData() : { data: { bankDetails: [] } }

  const { trustlyData = {} } = shouldFetchTrustlyData ? useGetTrustlyData() : { data: { data: [] } }

  useEffect(() => {
    if (shouldFetchBankData) {
      const { data: { bankDetails = [] } = {} } = payByBankData
      if (bankDetails?.length) {
        let selectedOption = {}
        const options = bankDetails.map((bankObj) => {
          if (email === bankObj?.id) {
            selectedOption = {
              value: bankObj?.id,
              label: bankObj.bankName
            }
          }
          return {
            value: bankObj.id,
            label: bankObj.bankName
          }
        })

        setBankAccountOptions(options)

        setSelectedBank(selectedOption)
      }
    }
  }, [payByBankData?.data, shouldFetchBankData])

  useEffect(() => {
    if (shouldFetchTrustlyData) {
      const { data: { data = [] } = {} } = trustlyData
      if (data?.length) {
        let selectedOption = {}
        const options = data?.map((bankObj) => {
          selectedOption = {
            value: bankObj?.trustlyTransactionId,
            label: bankObj.moreDetails?.bankName
          }

          return {
            value: bankObj.trustlyTransactionId,
            label: bankObj.moreDetails?.bankName
          }
        })

        setBankAccountOptions(options)
        setSelectedBank(selectedOption)
      }
    }
  }, [trustlyData?.data, shouldFetchTrustlyData])

  watch('providerType')

  const handleSubmitRedeem = (data) => {
    setIsDisable(true);
    setIsLoading(true);

    const payload = {
      transactionId: type === 'transaction' ? item.id : item?.transactionId || item?.transactionid,
      actionableEmail: isActionableEmail ? data.actionableEmail : data.bank
    }
    if (paymentProvider === 'TRUSTLY') {
      payload.providerType = paymentProvider
    } else {
      payload.providerType = paymentProvider
    }
    confirmRedeemRequest.mutate(payload)
  }

  const classes = useStyles()

  return (
    <Grid className={classes.cancelRedeemModal}>
      {isLoading && <p className='modal-loader'> Loading ....</p>}
      {type === 'transaction' ? (
        <Grid display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
          <Typography variant='heading' sx={{ color: '#FFFFFF' }}>
            Redeem
          </Typography>
        </Grid>
      ) : (
        <Grid display={'flex'} alignItems={'center'} color='#FFFFFF' padding='1rem'>
          <IconButton
            edge='start'
            color='inherit'
            onClick={() => (setShowEditRedeemData ? setShowEditRedeemData(null) : handleClose())}
            aria-label='close'
          >
            <Tooltip title='Back to requests' arrow>
              <ArrowBackIcon />
            </Tooltip>
          </IconButton>

          <Typography variant='heading'>Edit Redeem</Typography>
        </Grid>
      )}
      <form onSubmit={handleSubmit(handleSubmitRedeem)}>
        <Grid display={'flex'} justifyContent={'space-between'} className='reedem-kyc-content'>
          <Grid className='leftSection'>
            <Grid className='inputWrap'>
              <FormLabel className='label'>Redeem Amount</FormLabel>
              <br />
              <Grid className='input-grp'>
                <OutlinedInput
                  sx={{
                    width: '100%',
                    '& .MuiOutlinedInput-input': {
                      color: '#fff' // This sets the text color for both normal and disabled states
                    },
                    '& .Mui-disabled': {
                      '-webkit-text-fill-color': '#fff' // This ensures the color remains white when disabled
                    }
                  }}
                  id='outlined-basic'
                  variant='outlined'
                  placeholder='Amount'
                  {...register('amount')}
                  name='amount'
                  value={item.amount}
                  disabled
                />
              </Grid>
              {errors?.amount && (
                <p style={{ paddingTop: '15px' }} className={classes.inputError}>
                  {errors?.amount?.message}
                </p>
              )}
            </Grid>

            <Grid className='inputWrap'>
              <FormLabel className='label'>{isActionableEmail ? 'Skrill Email Address' : 'Select Bank'}</FormLabel>
              <br />

              {!isActionableEmail ? (
                // <Select
                //   name='bank'
                //   value={selectedBank}
                //   onChange={(data) => {
                //     setValue('bank', data.value)
                //     setSelectedBank(data)
                //   }}
                //   options={bankAccountOptions}
                //   className={'custom-select'}
                //   placeholder='Select Bank'
                //   isSearchable={false}
                //   components={{
                //     Input: (props) => <components.Input {...props} readOnly />
                //   }}
                // />
                <Grid className='input-grp'>
                  <OutlinedInput
                    sx={{
                      width: '100%',
                      '& .MuiOutlinedInput-input': {
                        color: '#fff' // Text color for normal state
                      },
                      '& .Mui-disabled': {
                        '-webkit-text-fill-color': '#fff' // Keep text white when disabled
                      }
                    }}
                    id='outlined-bank'
                    variant='outlined'
                    placeholder='Bank'
                    value={selectedBank?.label || ''}
                    disabled
                  />
                </Grid>
              ) : (
                <OutlinedInput
                  className='skrill-email'
                  variant='outlined'
                  id='Email_Address'
                  {...register('actionableEmail')}
                  placeholder='Skrill Email Address'
                  autoComplete='off'
                />
              )}

              {!!Object.keys(errors)?.length && (
                <p style={{ paddingTop: '3px', color: 'red' }} className={classes.inputError}>
                  {Object.keys(errors).map((key) => {
                    return errors[key]?.message || ''
                  })}
                </p>
              )}
            </Grid>

            <Grid>
              <Grid className='btn-wrap d-flex'>
                <Button
                  variant='contained'
                  className='btn btn-secondary'
                  disabled={isDisable}
                  onClick={() => (setShowEditRedeemData ? setShowEditRedeemData(null) : handleClose())}
                >
                  Go back
                </Button>
                <Button className='btn btn-primary' variant='contained' type='submit'>
                  Update Redeem
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </form>
    </Grid>
  )
}

export default EditCancelRedeem
