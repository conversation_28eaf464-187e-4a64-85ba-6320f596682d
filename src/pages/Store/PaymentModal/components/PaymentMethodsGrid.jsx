import React from 'react'
import { Grid, Box, Typography } from '@mui/material'
import { CardImg, SkrillImg, AppleImg, Bank } from '../../../../components/ui-kit/icons/svg'
import TrustlyBankIcons from '../../../../components/ui-kit/icons/png/trustly_bankicons.png'
import { usePaymentStore } from '../../../../store/usePaymentStore'
import toast from 'react-hot-toast'

const PaymentMethodsGrid = ({
  activePaymentMethods,
  selectedPaymentMethod,
  changePaymentMethod,
  setPaymentMethod,
  isAppleDevice,
  initiateTrustlyNewUserFlow,
  isLoading,
  preferredPayments
}) => {
  const { setPaymentTrustly, setPaymentStatus } = usePaymentStore()

  return (
    <Grid container justifyContent='center' spacing={{ xs: 0.5 }} className='payment-methods'>
      {(activePaymentMethods?.includes('PAY_BY_BANK') || activePaymentMethods?.includes('TRUSTLY')) && (
        <Grid item xs={isAppleDevice ? 6 : 4} sm={isAppleDevice ? 3 : 4}>
          <Box
            className={`payment-methods-item ${[3, 5].includes(selectedPaymentMethod) ? 'active' : ''}`}
            onClick={() => {
              if (!isLoading) {
                if (activePaymentMethods?.includes('TRUSTLY')) {
                  const hasTrustly = preferredPayments?.some((payment) => payment.paymentMethodType === 'TRUSTLY')
                  if (hasTrustly) {
                    setPaymentTrustly('isPreferredHasTrustly', true)
                    if (preferredPayments.length > 3) {
                      setPaymentStatus('showAllPayments', true)
                    }
                    toast.success('An account is already present in Preferred payment section')
                    return
                  }
                  changePaymentMethod(5)
                  setPaymentMethod('TRUSTLY')
                  initiateTrustlyNewUserFlow()
                  setPaymentTrustly('isTrustlyCheckboxSelected', true)
                } else {
                  changePaymentMethod(3)
                  setPaymentMethod('PAY_BY_BANK')
                  setPaymentTrustly('isTrustlyCheckboxSelected', false)
                }
              }
            }}
          >
            {activePaymentMethods?.includes('TRUSTLY')
              ? (<img src={TrustlyBankIcons} alt='pbb-img' style={{ height: '30px', paddingTop: '6px', width: '90px' }} />)
              : (<img src={Bank} alt='pbb-img' height='50px' width='50px' />)}
            <Typography sx={{ color: 'white', fontWeight: '500', whiteSpace: 'nowrap' }}>Online Banking</Typography>
          </Box>
        </Grid>
      )}
      {activePaymentMethods?.includes('CARD') && (
        <Grid item xs={isAppleDevice ? 6 : 4} sm={isAppleDevice ? 3 : 4}>
          <Box
            className={`payment-methods-item ${selectedPaymentMethod === 0 ? 'active' : ''}`}
            onClick={() => {
              changePaymentMethod(0)
              setPaymentMethod('CARD')
              setPaymentTrustly('isTrustlyCheckboxSelected', false)
            }}
          >
            <img src={CardImg} alt='card-img' height='30px' width='30px' />
            <Typography sx={{ color: 'white', fontWeight: '500' }}>Card</Typography>
          </Box>
        </Grid>
      )}

      {activePaymentMethods?.includes('SKRILL') && (
        <Grid item xs={isAppleDevice ? 6 : 4} sm={isAppleDevice ? 3 : 4}>
          <Box
            className={`payment-methods-item ${selectedPaymentMethod === 1 ? 'active' : ''}`}
            onClick={() => {
              changePaymentMethod(1)
              setPaymentMethod('SKRILL')
              setPaymentTrustly('isTrustlyCheckboxSelected', false)
            }}
          >
            <img src={SkrillImg} alt='skrill-img' height='40px' width='40px' />
            <Typography sx={{ color: 'white', fontWeight: '500' }}>Skrill</Typography>
          </Box>
        </Grid>
      )}

      {isAppleDevice && activePaymentMethods?.includes('APPLE_PAY') && (
        <Grid item xs={isAppleDevice ? 6 : 4} sm={isAppleDevice ? 3 : 4}>
          <Box
            className={`payment-methods-item ${selectedPaymentMethod === 2 ? 'active' : ''}`}
            onClick={() => {
              changePaymentMethod(2)
              setPaymentMethod('APPLE_PAY')
              setPaymentTrustly('isTrustlyCheckboxSelected', false)
            }}
          >
            <>
              <img src={AppleImg} alt='apple-img' height='40px' width='40px' />
              <Typography sx={{ color: 'white', fontWeight: '500' }}>Apple Pay</Typography>
            </>
          </Box>
        </Grid>
      )}

      {activePaymentMethods?.length === 0 && (
        <Typography className='no-active-pay'>No Active Payment Methods</Typography>
      )}
    </Grid>
  )
}

export default PaymentMethodsGrid
