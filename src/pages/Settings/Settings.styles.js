import { makeStyles } from '@mui/styles'

import { LobbyRight } from '../../MainPage.styles'

export default makeStyles((theme) => ({
  lobbyRight: {
    ...LobbyRight(theme),
    '& .settings-page-wrap': {
      maxWidth: theme.spacing(71),

      margin: '0 auto',
      background: theme.colors.settingsBg,
      borderRadius: theme.spacing(1.25),
      padding: theme.spacing(2.25, 2.8125),

      [theme.breakpoints.down('md')]: {
        padding: theme.spacing(2.25, 0)
      },
      '& .MuiTypography-root, & button': {}
    },
    '& .setting-tab-details': {
      margin: theme.spacing(1, 0),
      '& .genral-tab': {
        border: `1px solid ${theme.colors.modalTabBtnActive}`,
        borderRadius: theme.spacing(0.625),
        margin: theme.spacing(2, 0),
        overflow: 'hidden',
        '& .setting-card-header': {
          background: theme.colors.coinBundle,
          padding: theme.spacing(1, 2),
          borderRadius: theme.spacing(0.5625),
          minHeight: theme.spacing(4.1875),
          display: 'flex',
          alignItems: 'center',
          '& h4': {
            fontSize: theme.spacing(1.25),
            fontWeight: '500',
            display: 'flex',
            gap: theme.spacing(1.125)
          }
        },
        '& .setting-card-details': {
          padding: theme.spacing(1),

          '& .MuiFormControl-root': {
            // minWidth: theme.spacing(37.5),
            [theme.breakpoints.down('lg')]: {
              minWidth: '100%'
            },
            '& .MuiInputBase-root': {
              paddingRight: 0,
              '& .MuiInputBase-input': {
                border: `2px solid ${theme.colors.modalTabBtnActive}`,
                color: theme.colors.textWhite,
                borderRadius: theme.spacing(0.625),

                padding: theme.spacing(0.625, 1)
                // height:theme.spcing()
              },
              '&.Mui-focused': {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.colors.YellowishOrange,
                  borderRadius: theme.spacing(0.625)
                }
              }
            },
            '& .MuiFormLabel-root': {
              color: theme.colors.textWhite,
              lineHeight: theme.spacing(1)
            }
          },
          '& p': {
            marginBottom: theme.spacing(0.25)
          },
          '& .MuiIconButton-edgeEnd': {
            color: theme.colors.textWhite
          },
          '& .MuiInputAdornment-root': {
            position: 'absolute',
            right: theme.spacing(2)
          },
          '& .privacy-card': {
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(1),
            padding: theme.spacing(1.5, 0),
            // margin:theme.spacing(1.2, 0),
            '& h4': {
              fontSize: theme.spacing(1.125),
              fontWeight: '500',
              marginBottom: theme.spacing(0.313)
            },
            '& p': {
              color: theme.colors.switchText,
              fontWeight: '400',

              margin: '0'
            },
            '& .custom-radio-wrap': {
              '& .MuiButtonBase-root': {
                color: theme.colors.sidebarNavBg,
                background: theme.colors.switchTrackoff,
                padding: 0,
                marginRight: theme.spacing(1),
                '&.Mui-checked': {
                  color: theme.colors.YellowishOrange
                }
              },
              '& .MuiFormControlLabel-root': {
                paddingBottom: theme.spacing(1)
              }
            },
            '& .kyc-content': {
              '& h3': {
                fontWeight: '700',
                fontSize: theme.spacing(2.2),
                marginBottom: theme.spacing(0.313)
              },
              '&.kyc-success': {
                textAlign: 'center',
                width: '100%',
                '& .verified-img': {
                  height: theme.spacing(5),
                  width: theme.spacing(5),
                  margin: '0 auto 1rem'
                }
              }
            }
          },
          '& .preferred-box': {
            background: '#1C1C1C',
            borderRadius: '12px',
            '& .preferred-card': {
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              borderBottom: '1px solid #373737',
              padding: '10px 20px',
              '&:last-child': {
                borderBottom: 'none'
              },
              '& .card-detail': {
                display: 'flex',
                alignItems: 'center',
                gap: '2rem',
                '& img': {
                  width: '30px',
                  height: '20px'
                },
                '& .bank-details': {
                  width: 'fit-content',
                  [theme.breakpoints.down('md')]: {
                    // maxWidth: '155px'
                    width: 'fit-content'
                  },
                  '&::before': {
                    content: "''",
                    position: 'absolute',
                    left: '-1.25rem',
                    top: '0px',
                    height: '100%',
                    width: '1px',
                    background: '#494949'
                  },
                  '& .bank-name': {
                    fontSize: '14px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    color: '#fff',
                    gap: '0.25rem',
                    '& .MuiTypography-root': {
                      fontWeight: '700'
                    }
                  },
                  '& .bank-number': {
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '14px',
                    height: '14px',
                    color: '#606060',
                    '& .MuiTypography-root': {
                      fontWeight: '600'
                    },
                    '& .holder-name': {
                      color: '#b7b7b7',
                      fontWeight: '500',
                      fontSize: '16px',
                      lineHeight: '1.25rem',
                      '&.border-right': {
                        borderRight: '1px solid #b7b7b7',
                        marginRight: '6px',
                        paddingRight: '6px',
                        height: '15px',
                        display: 'flex',
                        alignItems: 'center',
                        paddingTop: '3.5px'
                      }
                    }
                  },
                  '& .bank-holder': {
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '14px',
                    height: '14px',
                    color: '#b7b7b7',
                    '& .MuiTypography-root': {
                      fontWeight: '600'
                    }
                  },
                  '& .bank-numbers': {
                    color: theme.colors.textWhite,
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '12px',
                    fontWeight: '600'
                  }
                }
              },
              '& .card-remove': {
                '& .remove-button': {
                  color: '#1e90ff',
                  fontSize: '18px',
                  textTransform: 'none',
                  padding: 0,
                  minWidth: 'unset',
                  textDecoration: 'underline'
                }
              }
            }
          }
        },
        '& .setting-card-footer': {
          padding: theme.spacing(1),
          borderTop: `1px solid ${theme.colors.modalTabBtnActive}`,
          display: 'flex',
          justifyContent: 'flex-end',
          gap: theme.spacing(1),
          '& .btn-primary': {
            color: theme.colors.textBlack,
            '&:hover': {
              color: theme.colors.YellowishOrange
            },
            '&:disabled': {
              color: theme.colors.textBlack,
              background: theme.colors.disabledBtn
            }
          },
          '&.privacy-footer': {
            justifyContent: 'space-between',
            alignItems: 'center',
            '& p': {
              margin: 0
            }
          }
        },
        '& .kyc-btn-wrap': {
          padding: theme.spacing(0, 2, 2),
          '& button': {
            width: '100%'
          }
        },
        '& .btn-primary': {
          color: theme.colors.textBlack,

          '&:hover': {
            color: theme.colors.YellowishOrange
          }
        }
      },
      '& .input-wrap': {
        marginBottom: theme.spacing(1),
        '& .MuiFormControl-root': {
          minWidth: '100% !important'
        },
        '&.copy-input': {
          maxWidth: theme.spacing(21.375),
          '& .MuiInputBase-input': {
            '& .MuiInputBase-input': {
              paddingRight: '0'
            }
          },
          '& .MuiInputBase-root': {
            padding: '0 !important',
            fontSize: theme.spacing(0.75),
            '& .MuiInputAdornment-root': {
              right: theme.spacing(0.125),
              '& .MuiButtonBase-root': {
                color: theme.colors.textWhite,
                background: theme.colors.copyBtn,
                borderRadius: theme.spacing(0.5),
                height: theme.spacing(2.5625),
                width: theme.spacing(3)
                // right:theme.spacing(1.875),
              }
            }
          },
          '& .scanner': {
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            '& .scanner-inner': {
              background: 'white',
              padding: '1px',
              width: '50%'
            }
          }
        }
      },
      '& p.kyc-text': {
        marginBottom: `${theme.spacing(2)} !important`
      }
    }
  },

  roundedThemeTabs: {
    border: `1px solid ${theme.colors.modalTabBtnActive}`,
    borderRadius: theme.spacing(1.875),
    padding: theme.spacing(0, 0.5),
    width: 'fit-content',
    minHeight: theme.spacing(3.4375),
    display: 'flex',
    alignItems: 'center',
    [theme.breakpoints.down('md')]: {
      width: '100%'
    },
    '& .MuiTabs-root': {
      [theme.breakpoints.down('md')]: {
        width: '100%'
      }
    },
    '& .MuiTabs-scrollButtons': {
      display: 'flex',
      padding: 0,
      minWidth: 'auto',
      width: '0.625rem'
    },
    '& .MuiTabs-flexContainer': {
      gap: '0.313rem',
      [theme.breakpoints.down('md')]: {
        width: '100%'
      }
    },
    '& .MuiTabs-scroller': {
      alignItems: 'center',
      display: 'flex',
      gap: theme.spacing(0.5),
      [theme.breakpoints.down('md')]: {
        width: '100%'
      },
      '& button': {
        color: theme.colors.textWhite,
        padding: theme.spacing(0.4, 1.2),
        position: 'relative',
        cursor: 'pointer',
        fontSize: theme.spacing(1),
        transition: 'none',
        transform: 'none',
        minWidth: 'auto',
        borderRadius: theme.spacing(4.1875),
        fontWeight: theme.typography.fontWeightExtraBold,
        display: 'flex',
        flexDirection: 'row',
        minHeight: theme.spacing(2.5),
        gap: theme.spacing(0.625),

        '&.Mui-selected': {
          background: theme.colors.YellowishOrange,
          color: theme.colors.textBlack,
          '& img': {
            filter: 'unset',
            [theme.breakpoints.down('md')]: {
              width: theme.spacing(1)
            }
          }
        },
        '&:hover': {
          borderRadius: theme.spacing(4.1875),
          background: theme.colors.YellowishOrange,
          transition: 'none',
          transform: 'none',
          color: theme.colors.textBlack,
          '& span': {
            color: theme.colors.textBlack
          },
          '& img': {
            filter: 'unset'
          }
        },
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0.4, 0.25),
          fontSize: theme.spacing(0.75),
          flexGrow: '1'
        },

        '&.active': {
          borderRadius: theme.spacing(4.1875),
          background: '#D6A300',
          fontWeight: theme.typography.fontWeightExtraBold,
          '& span': {
            color: theme.colors.textBlack
          }
        },
        '& img': {
          width: '1rem',
          filter: 'contrast(0.2)',
          marginBottom: '0'
        }
      }
    },
    '& .MuiTabs-indicator': {
      display: 'none'
    }
  },

  inputError: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    margin: '0 !important',
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600'
  },

  prefferdPaymentBox: {
    '& .preferred-card': {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottom: '1px solid #373737',
      padding: '10px 20px',
      '&:last-child': {
        borderBottom: 'none'
      },
      '& .card-detail': {
        display: 'flex',
        alignItems: 'center',
        gap: '2rem',
        '& img': {
          width: '30px',
          height: '20px'
        },
        '& .bank-details': {
          width: 'fit-content',
          [theme.breakpoints.down('md')]: {
            // maxWidth: '155px'
            width: 'fit-content'
          },
          '&::before': {
            content: "''",
            position: 'absolute',
            left: '-1.25rem',
            top: '0px',
            height: '100%',
            width: '1px',
            background: '#494949'
          },
          '& .bank-name': {
            fontSize: '14px',
            display: 'flex',
            justifyContent: 'space-between',
            color: '#fff',
            gap: '0.25rem',
            '& .MuiTypography-root': {
              fontWeight: '700'
            }
          },
          '& .bank-number': {
            display: 'flex',
            justifyContent: 'space-between',
            fontSize: '14px',
            height: '14px',
            color: '#606060',
            '& .MuiTypography-root': {
              fontWeight: '600'
            },
            '& .holder-name': {
              color: '#b7b7b7',
              fontWeight: '500',
              fontSize: '16px',
              lineHeight: '1.25rem',
              '&.border-right': {
                borderRight: '1px solid #b7b7b7',
                marginRight: '6px',
                paddingRight: '6px',
                height: '15px',
                display: 'flex',
                alignItems: 'center',
                paddingTop: '3.5px'
              }
            }
          },
          '& .bank-holder': {
            display: 'flex',
            justifyContent: 'space-between',
            fontSize: '14px',
            height: '14px',
            color: '#b7b7b7',
            '& .MuiTypography-root': {
              fontWeight: '600'
            }
          },
          '& .bank-numbers': {
            color: theme.colors.textWhite,
            display: 'flex',
            justifyContent: 'space-between',
            fontSize: '12px',
            fontWeight: '600'
          }
        }
      },
      '& .card-remove': {
        '& .remove-button': {
          color: '#1e90ff',
          fontSize: '18px',
          textTransform: 'none',
          padding: 0,
          minWidth: 'unset',
          textDecoration: 'underline'
        }
      }
    }
  },

  subscriptionsContainer: {
    '& .subscriptions-card': {
      border: `1px solid ${theme.colors.modalTabBtnActive}`,
      borderRadius: theme.spacing(0.625),
      margin: theme.spacing(2, 0),
      overflow: 'hidden',
      '& .subscription-table': {
        '& .table-head': {
          backgroundColor: '#1E1E1E',
          padding: theme.spacing(1),
          '& .table-head-text': {
            border: 'none',
            padding: theme.spacing(1),
            fontSize: theme.spacing(1.25),
            lineHeight: theme.spacing(1.5),
            color: theme.colors.textWhite,
            textAlign: 'center',
            fontWeight: '400',
            [theme.breakpoints.down('md')]: {
              minWidth: theme.spacing(6.25),
              fontSize: theme.spacing(1),
              padding: theme.spacing(0.75)
            }
          }
        },
        '& td': {
          fontWeight: '600'
        },
        '& .table-body': {
          '& .table-body-row': {
            border: 'none',
            '& .table-item-row': {
              border: 'none',
              padding: theme.spacing(1),
              fontSize: theme.spacing(1),
              color: theme.colors.textWhite,
              fontWeight: '500',
              [theme.breakpoints.down('md')]: {
                minWidth: theme.spacing(6.25)
              },
              '& .current-plan-sub-title': {
                color: '#FDB72E',
                fontSize: theme.spacing(1.25),
                fontWeight: '600'
              },
              '& .current-plan-description': {
                color: theme.colors.textWhite,
                fontSize: theme.spacing(1),
                fontWeight: '500'
              }
            }
          }
        }
      },
      '& .upgrade-button': {
        color: theme.colors.textWhite,
        padding: theme.spacing(0.4, 1.2),
        position: 'relative',
        cursor: 'pointer',
        fontSize: theme.spacing(1),
        transition: 'none',
        transform: 'none',
        minWidth: 'auto',
        borderRadius: theme.spacing(4.1875),
        border: `1px solid ${theme.colors.textWhite}`,
        fontWeight: theme.typography.fontWeightExtraBold,
        display: 'flex',
        flexDirection: 'row',
        minHeight: theme.spacing(2.5),
        gap: theme.spacing(0.625),
        '&:hover': {
          borderRadius: theme.spacing(4.1875),
          background: '#D6A300',
          transition: 'none',
          transform: 'none',
          color: theme.colors.textBlack,
          '& span': {
            color: theme.colors.textBlack
          },
          '& img': {
            filter: 'unset'
          }
        },
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0.4, 0.25),
          fontSize: theme.spacing(0.75),
          flexGrow: '1'
        }
      },
      '& .stop-auto-renew-button': {
        background: theme.colors.YellowishOrange,
        color: theme.colors.textBlack,
        padding: theme.spacing(0.4, 1.2),
        position: 'relative',
        cursor: 'pointer',
        fontSize: theme.spacing(1),
        transition: 'none',
        transform: 'none',
        minWidth: 'auto',
        borderRadius: theme.spacing(4.1875),
        fontWeight: theme.typography.fontWeightExtraBold,
        display: 'flex',
        flexDirection: 'row',
        minHeight: theme.spacing(2.5),
        gap: theme.spacing(0.625),
        '&:hover': {
          borderRadius: theme.spacing(4.1875),
          background: '#D6A300',
          transition: 'none',
          transform: 'none',
          color: theme.colors.textBlack,
          '& span': {
            color: theme.colors.textBlack
          },
          '& img': {
            filter: 'unset'
          }
        },
        [theme.breakpoints.down('md')]: {
          padding: theme.spacing(0.4, 0.25),
          fontSize: theme.spacing(0.75),
          flexGrow: '1'
        }
      }
    }
  }
}))
