import React, { useEffect } from 'react'
import useStyles from './style'
import { Box, Button, CircularProgress, Grid, Typography } from '@mui/material'
import { usePortalStore } from '../../store/userPortalSlice'
import BonusBadge from '../../components/ui-kit/icons/svg/bonus.svg'
import FreeTag from '../../components/ui-kit/icons/svg/free.svg'
import Specialbanner from '../../components/ui-kit/icons/svg/specialbanner.svg'
import offerGraphics from '../../components/ui-kit/icons/opImages/offer-graphics.webp'
import smallCoinGraphic from '../../components/ui-kit/icons/opImages/small-coin-graphic.webp'
import usePackages from '../Store/hooks/usePackages'
import { formatPriceWithCommas, formatValueWithB, formatAmount, formatDiscountAmount } from '../../utils/helpers'
import StepperForm from '../../components/StepperForm'
import BannerManagement from '../../components/BannerManagement'
import { useBannerStore } from '../../store/useBannerSlice'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import PaymentStatus from '../../components/PaymentStatus'
import usePaysafePayment from '../Store/PaymentModal/hooks/usePaysafe'
import PaymentLoader from '../../components/Loader/PaymentLoader'
import { usePaymentProcessStore } from '../../store/store'
import cardCoin2 from '../../components/ui-kit/icons/utils/card-coin2.webp'
import usdCash from '../../components/ui-kit/icons/utils/usd-cash.webp'
import scratchCardIcon from '../../components/ui-kit/icons/png/scratch-card-icon.png'
import { JoinTMFPlus, TMFPlus } from '../../components/ui-kit/icons/svg'
import LazyImage from '../../utils/lazyImage'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'
import { PlayerRoutes } from '../../routes'
import vipLock from '../../components/ui-kit/icons/svg/vip-lock.svg'
import packageGif from '../../components/ui-kit/icons/gif/package.gif'
// import PaymentConnectionPopup from '../Store/PaymentModal/components/PaymentConnectionPopup'
// import JackpotBadge from '../Jackpot/JackpotBadge'

const Package = () => {
  const { status } = useParams()
  const { setPackageData } = usePaysafePayment()
  const portalStore = usePortalStore()
  const navigate = useNavigate()
  const classes = useStyles()
  const { packagePage } = useBannerStore((state) => state)
  const location = useLocation()
  const { isPaymentScreenLoading, setIsPaymentScreenLoading } = usePaysafePayment()
  const cancelDepositOpen = usePaymentProcessStore((state) => state.cancelDepositOpen)
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)

  const { packageData, isloading, refetch } = usePackages()
  const packageRow = packageData?.rows?.[0]

  const handleBuyNow = (item) => {
    ; (function () {
      window._conv_q = window._conv_q || []
      _conv_q.push(['pushRevenue', 'credit', item, '100466670'])
    })()
    setPackageData(item)
    portalStore.openPortal(
      () => (
        <>
          <Box className='stepper-outer-box'>
            <StepperForm stepperCalledFor={'purchase'} packageDetails={item} />
          </Box>
        </>
      ),
      'StepperModal'
    )
  }

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const statuss = params.get('status')

    if (statuss === 'success' || statuss === 'failed' || statuss === 'cancelled') {
      const data = {
        transactionId: params.get('transactionId'),
        status: params.get('status'),
        paymentMethod: params.get('paymentMethod'),
        scCoin: params.get('scCoin'),
        gcCoin: params.get('gcCoin'),
        bonusSc: params.get('bonusSc'),
        bonusGc: params.get('bonusGc'),
        amount: params.get('amount')
      }
      handlePaymentSuccess(data)
    }
  }, [location])

  const handlePaymentSuccess = (data) => {
    refetch()
    portalStore.openPortal(() => <PaymentStatus paymentDetails={data} />, 'loginModal')
  }

  const hasSubscription = userSubscription?.subscriptionDetail !== null
  const packageDiscountPercentage = userSubscription?.subscriptionFeatureDetail?.PACKAGE_EXCLUSIVE_DISCOUNT
  const maxPackageDiscountPercentage = userSubscription?.subscriptionFeatureMaxValue?.PACKAGE_EXCLUSIVE_DISCOUNT

  const CheckSubscription = () => {
    if (hasSubscription && maxPackageDiscountPercentage && packageDiscountPercentage) {
      return (
        <Grid>
          <Box className='subscribe-container'>
            <img src={packageGif} className='tmf-gif' alt='gif' />
            <Box className='tmf-plus-exclusive'>
              <Box className='non-subscribe-offers'>
                YOU’RE ENJOYING {packageDiscountPercentage}% OFF ON EXCLUSIVE TMF+ DEALS!
              </Box>
              <Typography className='non-subscribe-offers'>
                KEEP SPINNING — more surprises are on the way!
              </Typography>
            </Box>
          </Box>
        </Grid>
      )
    } else if (!hasSubscription && maxPackageDiscountPercentage && !packageDiscountPercentage) {
      return (
        <Grid>
          <Box className='non-subscribe-container'>
            <Box className='left-section'>
              <img src={packageGif} className='tmf-gif' alt='gif' />
              <Box className='tmf-plus-exclusive'>
                <Box className='tmf-text'>
                  {' '}
                  <LazyImage
                    src={JoinTMFPlus}
                    alt='TMF+'
                    sizes='100px'
                    priority
                    style={{ cursor: 'pointer', height: '2rem' }}
                  />{' '}
                  <span className='non-subscribe-offers'> TO GET EXCLUSIVE OFFERS.</span>
                </Box>
                <Typography className='non-subscribe-exclusive'>
                  Exclusive bonuses and surprises await. One spin could change everything!
                </Typography>
              </Box>
            </Box>

            <Button type='button' className='join-now' onClick={() => navigate(PlayerRoutes.Subscriptions)}>
              JOIN NOW
            </Button>
          </Box>
        </Grid>
      )
    } else if (hasSubscription && maxPackageDiscountPercentage && !packageDiscountPercentage) {
      return (
        <Grid>
          <Box className='non-subscribe-container'>
            <Box className='left-section'>
              <img src={packageGif} className='tmf-gif' alt='gif' />
              <Box className='tmf-plus-exclusive'>
                <Box className='tmf-text'>
                  <span className='non-subscribe-offers'> UPGRADE </span>
                  {' '}
                  <LazyImage
                    src={TMFPlus}
                    alt='TMF+'
                    sizes='100px'
                    priority
                    style={{ cursor: 'pointer', height: '2rem' }}
                  />{' '}
                  <span className='non-subscribe-offers'> TO GET EXCLUSIVE OFFERS.</span>
                </Box>
                <Typography className='non-subscribe-exclusive'>
                  Exclusive bonuses and surprises await. One spin could change everything!
                </Typography>
              </Box>
            </Box>

            <Button type='button' className='join-now' onClick={() => navigate(PlayerRoutes.Subscriptions)}>
              UPGRADE NOW
            </Button>
          </Box>
        </Grid>
      )
    } else {
      return null
    }
  }

  return (
    <>
      {status && <PaymentStatus status={status} isTrustly />}
      {(isPaymentScreenLoading || cancelDepositOpen) && <PaymentLoader />}
      <Grid className={classes.lobbyRight}>
        <Box className='package-page'>
          <Grid>
            <BannerManagement bannerData={packagePage} />
          </Grid>

          {isloading && (
            <div>
              <CircularProgress size={24} style={{ marginLeft: 8 }} />
              Loading...
            </div>
          )}

          {packageRow?.isSpecialPackage === true && (
            <Grid className='special-offer-section'>
              <Grid className='package-card'>
                <Grid container spacing={0.5}>
                  <Grid item xs={6} lg={3}>
                    <img
                      src={packageRow?.imageUrl ? packageRow?.imageUrl : offerGraphics}
                      alt='Offer'
                      className='offer-graphics'
                    />
                  </Grid>
                  <Grid item xs={12} lg={6} className='package-details-grid'>
                    <Box className={classes.container}>
                      <Grid className='package-details' spacing={0.5} justifyContent='center' alignItems='center'>
                        <Grid className='package-content'>
                          <Grid className='package-price'>
                            <img src={cardCoin2} alt='coins' />
                            {packageRow?.gcCoin} GC +
                          </Grid>
                          <Grid className='package-price'>
                            <img src={FreeTag} alt='free' className='free-tag' />
                          </Grid>
                          <Grid className='package-price'>
                            <img src={usdCash} alt='free' />
                            {packageRow?.scCoin} SC
                          </Grid>
                        </Grid>
                        <Button
                          className='btn btn-primary'
                          onClick={() => handleBuyNow(packageRow)}
                          data-tracking={`Store.${packageRow?.packageName}.Offer`}
                          data-tracking-item-id={packageRow?.packageId}
                          data-tracking-item-price={formatAmount(packageRow?.amount)}
                          data-tracking-item-name={`${packageRow?.packageName}`}
                          data-tracking-item-list={'User.Store.Main'}
                          data-tracking-item-catalog={'Special_Package'}
                        >
                          {userSubscription?.subscriptionDetail !== null && packageDiscountPercentage
                            ? (
                              // Case 1: Subscribed & Subscriber-only
                              <>
                                <del className='original-price'>
                                  ${formatAmount(packageRow?.amount)}
                                </del>{' '}
                                <span className='discounted-price'>
                                  ${formatDiscountAmount(packageRow?.amount, packageData?.subscriberExclusiveDiscountPercentage)}
                                </span>
                              </>)
                            : userSubscription?.subscriptionDetail === null && packageRow?.isSubscriberOnly && maxPackageDiscountPercentage
                              ? (
                                // Case 2: Not subscribed & Subscriber-only
                                <>
                                  <img src={vipLock} alt='lock' className='tmf-lock-icon' style={{ height: '1.25rem', marginRight: '0.5rem', color: 'black' }} />
                                  ${formatAmount(packageRow?.amount)}
                                </>)
                              : (
                                // Case 3: Everything else
                                <>${formatAmount(packageRow?.amount)}</>)}
                        </Button>
                      </Grid>
                    </Box>
                  </Grid>
                  <Grid item xs={6} lg={3} className='badge-grid'>
                    <Grid className='offer-badge'>
                      <img src={Specialbanner} alt='Offer' />
                      <Grid className='sp-offer-badge-content'>
                        {packageRow?.bonusPercentage > 0 ? (
                          <>
                            <Typography variant='h5'>{packageRow?.bonusPercentage}%</Typography>
                            <Typography>Bonus</Typography>
                          </>
                        ) : (
                          <Typography>Special Offer</Typography>
                        )}
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          )}
          <Grid className='purchase-section'>
            <Grid className='inner-heading'>
              <Typography variant='h4'>Purchase Package</Typography>
            </Grid>
            <Grid className='purchase-section-content'>
              <CheckSubscription />
              {packageData?.rows?.length > 0
                ? packageData?.rows
                  ?.filter((packageRow) => !packageRow?.isSpecialPackage || packageRow?.isSpecialPackage === true)
                  ?.slice(packageRow?.isSpecialPackage ? 1 : 0)
                  .map((item, index) => {
                    return (
                      <Grid
                        className={`package-card tmf-plus ${hasSubscription && item?.isSubscriberOnly && maxPackageDiscountPercentage && packageDiscountPercentage
                          ? ''
                          : userSubscription?.subscriptionDetail === null && item?.isSubscriberOnly && maxPackageDiscountPercentage && !packageDiscountPercentage
                            ? 'tmf-plus-user-card'
                            : ''
                          }`}
                        key={`package-${item?.packageId}`}
                      >
                        {item?.isSubscriberOnly && maxPackageDiscountPercentage && (
                          <Box className='subscribe-only-package'>
                            <Typography>TMF Plus Exclusive</Typography>
                          </Box>
                        )}

                        <Grid container spacing={0.5}>
                          <Grid item xs={6} md={6} lg={3}>
                            <Grid className='package-img-wrap order-1'>
                              <>
                                {item?.bonusPercentage > 0 && (
                                  <Grid className='package-img'>
                                    <img src={BonusBadge} alt='Badge' className='bonus-badge' />
                                    <Grid className='offer-badge-content-text'>
                                      <Typography variant='h5'>{item?.bonusPercentage}%</Typography>
                                      <Typography>Bonus</Typography>
                                    </Grid>
                                  </Grid>
                                )}
                              </>
                              <img
                                src={item?.imageUrl ? item?.imageUrl : smallCoinGraphic}
                                alt='Bonus'
                                className='bonus-graphics'
                              />
                            </Grid>
                          </Grid>
                          <Grid item xs={12} md={12} lg={6} className='order-3'>
                            <Grid className='package-details' spacing={1} justifyContent='center' alignItems='center'>
                              <Grid className='package-content'>
                                <Grid className='package-price'>
                                  <img src={cardCoin2} alt='coins' />
                                  {item?.gcCoin > 0
                                    ? formatPriceWithCommas(formatValueWithB(Number(item?.gcCoin)))
                                    : item?.gcCoin}{' '}
                                  GC +
                                </Grid>
                                <Grid className='package-price'>
                                  <img src={FreeTag} alt='free' className='free-tag' />
                                </Grid>
                                <Grid className='package-price'>
                                  <img src={usdCash} alt='free' />
                                  {item?.scCoin > 0
                                    ? formatPriceWithCommas(formatValueWithB(Number(item?.scCoin)))
                                    : item?.scCoin}{' '}
                                  SC
                                </Grid>
                                {item?.isScratchCardExist && <Grid className='package-price extra-prize'>
                                  + <img src={scratchCardIcon} />{' '}
                                  <Typography className='scratch-price' variant='span'>
                                    SCRATCH CARD
                                  </Typography>
                                </Grid>}
                                {item?.isFreeSpinExist &&
                                  <Grid className='package-price extra-prize'>
                                    + <img src={scratchCardIcon} />{' '}
                                    <Typography className='scratch-price' variant='span'>
                                      SCRATCH CARD
                                    </Typography>
                                  </Grid>
                                }
                                {item?.freeSpinId && (
                                  <Grid className='package-price extra-prize'>
                                    + <img src={scratchCardIcon} />{' '}
                                    <Typography className='scratch-price' variant='span'>
                                      FREE SPIN
                                    </Typography>
                                  </Grid>
                                )}
                              </Grid>
                            </Grid>
                            {item?.isScratchCardExist && <Grid className='package-price mob-extra-prize'>
                              + <img src={scratchCardIcon} />{' '}
                              <Typography className='scratch-price' variant='span'>
                                SCRATCH CARD
                              </Typography>
                            </Grid>}
                            {item?.isFreeSpinExist && <Grid className='package-price mob-extra-prize'>
                              + <img src={scratchCardIcon} />{' '}
                              <Typography className='scratch-price' variant='span'>
                                FREE SPIN
                              </Typography>
                            </Grid>}

                          </Grid>
                          <Grid item xs={6} md={6} lg={3} className='order-2'>
                            <Grid className='package-btn-wrap'>
                              <Button
                                className='btn btn-primary'
                                disabled={userSubscription?.subscriptionDetail === null && item?.isSubscriberOnly}
                                onClick={() => handleBuyNow(item)}
                                data-tracking={`Store.${item?.packageName}.Offer`}
                                data-tracking-item-id={item?.packageId}
                                data-tracking-item-price={formatAmount(item?.amount)}
                                data-tracking-item-name={`${item?.packageName}`}
                                data-tracking-item-list={'User.Store.Main'}
                                data-tracking-item-catalog={'Basic_Package'}
                              >
                                {userSubscription?.subscriptionDetail !== null && packageDiscountPercentage
                                  ? (
                                    // Case 1: Subscribed & Subscriber-only
                                    <>
                                      <del className='original-price'>
                                        ${formatAmount(item?.amount)}
                                      </del>{' '}
                                      <span className='discounted-price'>
                                        ${formatDiscountAmount(item?.amount, packageData?.subscriberExclusiveDiscountPercentage)}
                                      </span>
                                    </>)
                                  : userSubscription?.subscriptionDetail === null && item?.isSubscriberOnly && maxPackageDiscountPercentage
                                    ? (
                                      // Case 2: Not subscribed & Subscriber-only
                                      <>
                                        <img src={vipLock} alt='lock' className='tmf-lock-icon' style={{ height: '1.25rem', marginRight: '0.5rem', color: 'black' }} />
                                        ${formatAmount(item?.amount)}
                                      </>)
                                    : (
                                      // Case 3: Everything else
                                      <>${formatAmount(item?.amount)}</>)}
                              </Button>
                            </Grid>
                          </Grid>
                        </Grid>
                      </Grid>
                    )
                  })
                : 'No Active Packages '}
            </Grid>
          </Grid>
        </Box>
      </Grid>
    </>
  )
}

export default Package
