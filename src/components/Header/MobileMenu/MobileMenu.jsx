import React, { useEffect, useRef, useState } from 'react'
import List from '@mui/material/List'
import useStyles from './mobilemenu.styles'
import { Grid, ListItem, Typography } from '@mui/material'
// import { usdIcon, usdchipIcon } from '../../ui-kit/icons/svg'
import { usdIcon, usdchipIcon } from '../../ui-kit/icons/opImages'
import { KeyboardArrowDown as KeyboardArrowDownIcon, KeyboardArrowUp as KeyboardArrowUpIcon } from '@mui/icons-material'
import { formatPriceWithCommas } from '../../../utils/helpers'
import { useCoinStore } from '../../../store/store'
import useGetDeviceType from '../../../utils/useGetDeviceType'

export default function MobileMenu({ userDetails }) {
  const [show, setShow] = useState(false)
  const walletRef = useRef(null)
  const { isMobile } = useGetDeviceType()

  function useOutsideAlerter(ref) {
    useEffect(() => {
      /**
       * Alert if clicked on outside of element
       */
      if (isMobile) {
        function handleClickOutside(event) {
          if (isMobile && ref.current && !ref.current.contains(event.target)) {
            setShow(false)
          }
        }
        // Bind the event listener
        document.addEventListener('mousedown', handleClickOutside)
        return () => {
          // Unbind the event listener on clean up
          document.removeEventListener('mousedown', handleClickOutside)
        }
      }
    }, [ref, isMobile])
  }
  useOutsideAlerter(walletRef)

  const classes = useStyles()

  const [showSweepCoinTop, setShowSweepCoinTop] = useState(true)

  const setCoin = useCoinStore((state) => {
    return state.setCoinType
  })

  const coinType = useCoinStore((state) => state.coinType)

  useEffect(() => {
    if (coinType === 'GC') {
      setShowSweepCoinTop(false)
      setCoin('GC')
    } else {
      setShowSweepCoinTop(true)
      setCoin('SC')
    }
  }, [coinType])

  const handleClick = (coin) => {
    if (coin === 'goldCoin') {
      setShowSweepCoinTop(false)
      setCoin('GC')
    } else {
      setShowSweepCoinTop(true)
      setCoin('SC')
    }
  }

  // useEffect(() => {
  //   const handleClickOutside = (event) => {
  //     if (walletRef.current && !walletRef.current.contains(event.target)) {
  //       setShow(false)
  //     }
  //   };

  //   document.addEventListener('click', handleClickOutside);

  //   return () => {
  //     document.removeEventListener('click', handleClickOutside);
  //   };
  // }, []);

  const ShowSweepCoinOnTop = () => {
    return (
      <>
        <ListItem>
          <Grid sx={{ display: 'flex', gap: '5px' }}>
            {/* <img src={usdIcon} alt='usdIcon' height='100%' width='100%' /> */}
            <div
              style={{
                backgroundImage: `url(${usdIcon})`,
                backgroundSize: '100% 100%',
                backgroundRepeat: 'no-repeat',
                height: '20px',
                width: '20px'
              }}
            />
            <Typography className='primaryText'>
              {formatPriceWithCommas(userDetails?.userWallet?.totalScCoin)} SC
            </Typography>
          </Grid>

          {/* <Typography className='secondaryText'>
            <Typography variant='span'>
              Win-
            </Typography>
            {formatPriceWithCommas(userDetails?.userWallet?.scCoin?.wsc)} SC
          </Typography> */}
        </ListItem>

        <ListItem onClick={() => handleClick('goldCoin')}>
          <Grid sx={{ display: 'flex', gap: '5px' }}>
            {/* <img src={usdchipIcon} alt='usdIcon' height='100%' width='100%' /> */}
            <div
              style={{
                backgroundImage: `url(${usdchipIcon})`,
                backgroundSize: '100% 100%',
                backgroundRepeat: 'no-repeat',
                height: '20px',
                width: '20px'
              }}
            />
            <Typography className='primaryText'>{formatPriceWithCommas(userDetails?.userWallet?.gcCoin)} GC</Typography>
          </Grid>
        </ListItem>
      </>
    )
  }

  const ShowGoldCoinOnTop = () => {
    return (
      <>
        <ListItem>
          <Grid sx={{ display: 'flex', gap: '5px' }}>
            {/* <img src={usdchipIcon} alt='usdIcon' height='100%' width='100%' /> */}
            <div
              style={{
                backgroundImage: `url(${usdchipIcon})`,
                backgroundSize: '100% 100%',
                backgroundRepeat: 'no-repeat',
                height: '20px',
                width: '20px'
              }}
            />
            <Typography className='primaryText'>{formatPriceWithCommas(userDetails?.userWallet?.gcCoin)} GC</Typography>
          </Grid>
        </ListItem>
        <ListItem onClick={() => handleClick('sweepCoin')}>
          <Grid sx={{ display: 'flex', gap: '5px' }}>
            {/* <img src={usdIcon} alt='usdIcon' height='100%' width='100%' /> */}
            <div
              style={{
                backgroundImage: `url(${usdIcon})`,
                backgroundSize: '100% 100%',
                backgroundRepeat: 'no-repeat',
                height: '20px',
                width: '20px'
              }}
            />
            <Typography className='primaryText'>
              {formatPriceWithCommas(userDetails?.userWallet?.totalScCoin)} SC
            </Typography>
          </Grid>

          {/* <Typography className='secondaryText'>
            <Typography variant='span'>
              Win-
            </Typography>
            {formatPriceWithCommas(userDetails?.userWallet?.scCoin?.wsc)} SC
          </Typography> */}
        </ListItem>
      </>
    )
  }
  return (
    <div
      id='walletMobile'
      ref={walletRef}
      className={show ? `${classes.mobileMenuList} hide` : `${classes.mobileMenuList} show`}
      onClick={() => setShow(!show)}
    >
      <List>{showSweepCoinTop ? <ShowSweepCoinOnTop /> : <ShowGoldCoinOnTop />}</List>
      <Grid className='arrowIcon'>{show ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}</Grid>
    </div>
  )
}
