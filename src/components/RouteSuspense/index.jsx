import { Suspense } from 'react'
import Loader from '../Loader'

/**
 * # RouteSuspense Component
 *
 * ## Purpose
 * A reusable Suspense wrapper specifically designed for route-level code splitting.
 * Provides consistent loading experience across all lazy-loaded routes and reduces
 * code duplication in route definitions.
 *
 * ## Why Route-based Code Splitting?
 *
 * ### The Problem:
 * - Without code splitting: All route components load upfront → Large initial bundle
 * - Users download code for pages they may never visit
 * - Slower Time to Interactive (TTI) and First Contentful Paint (FCP)
 *
 * ### The Solution:
 * - Lazy load route components → Smaller initial bundle
 * - Components download only when routes are accessed
 * - Better performance metrics and user experience
 *
 * ## How It Works:
 *
 * ### 1. Traditional Approach (Before):
 * ```javascript
 * import AboutPage from './AboutPage'  // Loaded immediately
 * import ContactPage from './ContactPage'  // Loaded immediately
 *
 * // Result: 500KB+ initial bundle
 * ```
 *
 * ### 2. Code Splitting Approach (After):
 * ```javascript
 * const AboutPage = lazy(() => import('./AboutPage'))  // Loaded on-demand
 * const ContactPage = lazy(() => import('./ContactPage'))  // Loaded on-demand
 *
 * // Result: 50KB initial bundle + chunks loaded as needed
 * ```
 *
 * ### 3. Bundle Structure:
 * ```
 * dist/
 * ├── main.bundle.js (50KB)        # Core app + critical routes
 * ├── about-page.chunk.js (25KB)   # Loads when /about accessed
 * ├── contact-page.chunk.js (30KB) # Loads when /contact accessed
 * └── dashboard.chunk.js (100KB)   # Loads when /dashboard accessed
 * ```
 *
 * ## Usage Examples:
 *
 * ### Basic Usage:
 * ```javascript
 * import RouteSuspense from './components/RouteSuspense'
 *
 * const LazyAboutPage = lazy(() => import('./AboutPage'))
 *
 * // In route definition:
 * {
 *   path: '/about',
 *   element: (
 *     <RouteSuspense routeName="About Page">
 *       <LazyAboutPage />
 *     </RouteSuspense>
 *   )
 * }
 * ```
 *
 * ### Custom Loading State:
 * ```javascript
 * const CustomLoader = () => <div>Loading About Page...</div>
 *
 * <RouteSuspense
 *   routeName="About Page"
 *   fallback={<CustomLoader />}
 * >
 *   <LazyAboutPage />
 * </RouteSuspense>
 * ```
 *
 * ### Comparison - Before vs After:
 * ```javascript
 * // BEFORE (Repetitive):
 * {
 *   path: '/about',
 *   element: (
 *     <Suspense fallback={<Loader />}>
 *       <LazyAboutPage />
 *     </Suspense>
 *   )
 * },
 * {
 *   path: '/contact',
 *   element: (
 *     <Suspense fallback={<Loader />}>
 *       <LazyContactPage />
 *     </Suspense>
 *   )
 * }
 *
 * // AFTER (Clean & Consistent):
 * {
 *   path: '/about',
 *   element: (
 *     <RouteSuspense routeName="About Page">
 *       <LazyAboutPage />
 *     </RouteSuspense>
 *   )
 * },
 * {
 *   path: '/contact',
 *   element: (
 *     <RouteSuspense routeName="Contact Page">
 *       <LazyContactPage />
 *     </RouteSuspense>
 *   )
 * }
 * ```
 *
 * ## Benefits:
 *
 * ### Performance:
 * - ✅ Reduced initial bundle size (faster first load)
 * - ✅ Better Time to Interactive (TTI)
 * - ✅ Improved Core Web Vitals scores
 * - ✅ Better caching (chunks can be cached independently)
 *
 * ### Developer Experience:
 * - ✅ Consistent loading states across all routes
 * - ✅ Centralized loading behavior management
 * - ✅ Debug logging in development mode
 * - ✅ Reduced code duplication
 *
 * ### User Experience:
 * - ✅ Faster initial page load
 * - ✅ Progressive loading of features
 * - ✅ Consistent loading indicators
 *
 * ## Future Development Guidelines:
 *
 * ### When to Use Lazy Loading:
 * - ✅ Large feature pages (Admin panels, Dashboards)
 * - ✅ Pages with heavy dependencies (Charts, Editors)
 * - ✅ User-specific pages (Profile, Settings)
 * - ✅ Less frequently accessed pages (Help, Terms)
 *
 * ### When NOT to Use:
 * - ❌ Critical path components (Header, Footer)
 * - ❌ Very small components (< 5KB)
 * - ❌ Components needed immediately on app start
 *
 * ### Best Practices:
 * 1. Group related components into logical chunks
 * 2. Monitor chunk sizes (keep under 250KB)
 * 3. Use meaningful route names for debugging
 * 4. Test loading states on slow networks
 * 5. Implement error boundaries around lazy routes
 *
 * @param {Object} props - Component props
 * @param {React.ComponentType} props.children - The lazy-loaded component to wrap
 * @param {React.ComponentType} [props.fallback] - Custom fallback component (defaults to Loader)
 * @param {string} [props.routeName] - Route name for debugging (development only)
 *
 * @example
 * // Basic usage
 * <RouteSuspense routeName="User Dashboard">
 *   <LazyUserDashboard />
 * </RouteSuspense>
 *
 * @example
 * // With custom loader
 * <RouteSuspense
 *   routeName="Admin Panel"
 *   fallback={<AdminPanelSkeleton />}
 * >
 *   <LazyAdminPanel />
 * </RouteSuspense>
 */
const RouteSuspense = ({
  children,
  fallback = <Loader />,
  routeName = 'Unknown Route'
}) => {
  // In development, add route name to help with debugging lazy loading
  if (import.meta.env.DEV && routeName !== 'Unknown Route') {
    console.debug(`🔄 Loading route: ${routeName}`)
  }

  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  )
}

export default RouteSuspense
