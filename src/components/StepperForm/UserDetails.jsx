import { useState, useEffect, useRef } from 'react'
import { Button, Typography, Box, Grid } from '@mui/material'

import usePersonalInfo from '../../pages/Accounts/hooks/useProfile'
import useStyles from './StepperForm.styles'
import useStepperStore from '../../store/useStepperStore'
import TagManager from 'react-gtm-module';
import { isEqual } from '../../utils/lodashLite'

const days = Array.from({ length: 31 }, (_, i) => i + 1)

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December'
]

const currentYear = new Date().getFullYear()
const years = Array.from({ length: currentYear - 1900 - 18 + 1 }, (_, i) => currentYear - 18 - i)

const UserDetails = ({ packageDetails }) => {
  const classes = useStyles()
  const { handleNext, stepperCalledFor } = useStepperStore((state) => state)

  const {
    handleSubmit,
    register,
    selectedState,
    errors,
    selectedDate,
    stateList: stateData,
    setValue,
    onStateChangeHandler,
    initialState,
    userDetails,
    setSelectedState,
    handleOnFormSubmit,
    setSelectedDate,
    genderOptions,
    selectedGender,
    onGenderChangeHandler,
    watch
  } = usePersonalInfo(stepperCalledFor)

  const [formError, setFormError] = useState({
    dobError: '',
    fullNameError: ''
  })
  const [day, setDay] = useState('')
  const [month, setMonth] = useState('')
  const [year, setYear] = useState('')
  const hasSetInitialDate = useRef(false)
  // const [fullName, setFullName] = useState('');

  const addressLine1Value = watch('addressLine_1')

  const handleDateValidation = (isValid, message, date) => {
    setValue('dateOfBirth', date)
    setSelectedDate(date)
    if (!isValid) {
      setFormError({ ...formError, dobError: 'You must be at least 18 years old.' }) // Set error if the selected date is not valid
    } else {
      setFormError({ ...formError, dobError: '' }) // Clear error if the selected date is valid
    }
  }

  // const isFieldDisabled = (field) => {
  //      const kycStatus = userDetails?.kycStatus;

  //   if ((field === 'day' || field ==='month' || field ==='year')){
  //     if(userDetails?.dateOfBirth === null || userDetails?.dateOfBirth === '1970-01-01')
  //         return false; // Editable
  //     else
  //         return true;
  //   }

  //     if(userDetails?.[field] && kycStatus !=='K1')
  //         return true;

  //     return false; // All fields are editable if KYC status is not K1 and field has no content
  // };

  const getDaysInMonth = (month, year) => {
    const monthIndex = months.indexOf(month) + 1
    return new Date(year, monthIndex, 0).getDate()
  }

  let autoComplete
  let address1Field
  let postalField

  useEffect(() => {
    if (stepperCalledFor === 'purchase') {
      TagManager.dataLayer({
        dataLayer: {
          event: 'checkout_personal_details',
          user_id: userDetails?.userId,
          email: userDetails?.email,
          item_id: packageDetails?.packageId,
          item_name: packageDetails?.packageName,
          price: packageDetails?.amount,
          catalog: packageDetails?.isSpecialPackage ? 'Special_Package' : 'Basic_Package',
          gcCoin: packageDetails?.gcCoin,
          scCoin: packageDetails?.scCoin
        }
      })
    }
  }, [])

  useEffect(() => {
    try {
      if (window.google && window.google.maps) {
        address1Field = document.querySelector('#ship-address')
        postalField = document.querySelector('#postcode')
        autoComplete = new window.google.maps.places.Autocomplete(address1Field, {
          componentRestrictions: { country: ['us'] },
          fields: ['address_components', 'geometry'],
          types: ['address']
        })
        autoComplete.addListener('place_changed', fillInAddress)
      }
    } catch (error) {
      console.error('GOOGLE MAPS ERROR in initializeGoogleMapsAPI:', error)
    }
  }, [])

  useEffect(() => {
    if (!addressLine1Value) {
      setValue('city', '')
      setValue('state', '')
      setValue('zipCode', '')
      setSelectedState('')
    }
  }, [addressLine1Value])
  useEffect(() => {
    if (userDetails?.dateOfBirth && !hasSetInitialDate.current) {
      const [year, month, day] = userDetails.dateOfBirth.split('-')

      const newMonth = months[parseInt(month) - 1]
      setDay(parseInt(day))
      setMonth(newMonth)
      setYear(year)

      hasSetInitialDate.current = true
    }
  }, [userDetails?.dateOfBirth, months])

  // Trigger validation every time day, month, or year is changed
  useEffect(() => {
    if (day && month && year) {
      if (day > getDaysInMonth(month, year)) {
        setValue('dateOfBirth', '')
        setSelectedDate('')
      } else {
        // Format date as 'YYYY-MM-DD'
        const formattedDate = `${year}-${String(months.indexOf(month) + 1).padStart(2, '0')}-${String(day).padStart(
          2,
          '0'
        )}`

        const selectedDate = new Date(`${year}-${months.indexOf(month) + 1}-${day}`)
        const today = new Date()
        const eighteenYearsAgo = new Date(today.setFullYear(today.getFullYear() - 18))

        if (selectedDate > eighteenYearsAgo) {
          handleDateValidation(
            false,
            `You must be at least 18 years old. (Selected date: ${formattedDate})`,
            formattedDate
          )
        } else {
          handleDateValidation(true, `Selected date: ${formattedDate}`, formattedDate)
        }
      }
    }
  }, [
    day,
    month,
    year,
    // handleDateValidation,
    months
  ])

  const availableDays = days ? Array.from({ length: getDaysInMonth(month, year) }, (_, i) => i + 1) : []

  const fillInAddress = () => {
    const place = autoComplete.getPlace()
    let address1 = ''
    let postcode = ''
    let city = ''
    let stateCode = ''
    for (const component of place.address_components) {
      const componentType = component.types[0]

      switch (componentType) {
        case 'street_number': {
          address1 = `${component.long_name} ${address1}`
          break
        }

        case 'route': {
          address1 += component.short_name
          break
        }

        case 'postal_code': {
          postcode = `${component.long_name}${postcode}`
          break
        }

        case 'postal_code_suffix': {
          postcode = `${postcode}-${component.long_name}`
          break
        }
        case 'locality':
          city = component.long_name
          document.querySelector('#locality').value = component.long_name
          break
        case 'administrative_area_level_1': {
          stateCode = component.short_name
          document.querySelector('#state').value = component.short_name
          break
        }
      }
    }
    address1Field.value = address1
    postalField.value = postcode
    setValue('addressLine_1', address1, { shouldValidate: true })
    setValue('city', city, { shouldValidate: true })
    setValue('state', stateCode, { shouldValidate: true })
    setValue('zipCode', postcode, { shouldValidate: true })
    setSelectedState(stateCode)
  }

  const handleFormSubmit = (formData) => {
    // const kycStatus = userDetails?.kycStatus;
    if (!selectedDate) {
      setFormError({ ...formError, dobError: 'Date of birth is required' })
      return
    }
    formData.dateOfBirth = selectedDate
    if (!isEqual(formData, initialState)) {
      handleOnFormSubmit(formData, selectedDate)
    } else {
      handleNext()
    }
  }

  return (
    <Grid>
      <Box className='personal-details-container'>
        <Typography className='personal-details-text'>PERSONAL DETAILS</Typography>
      </Box>
      <Box className='small-subheader-container'>
        <Typography className='small-subheader-text'>
          Please provide your details. We need to verify your ID.
        </Typography>
      </Box>

      <Box className='user-details-section'>
        <form onSubmit={handleSubmit(handleFormSubmit)} action='' autoComplete='off'>
          <Box className='user-details-input-wrap'>
            <Typography className='user-detail-label'>First Name</Typography>

            <input
              type='text'
              placeholder='Enter First Name'
              className='user-details-input'
              // disabled={isFieldDisabled('firstName')}
              {...register('firstName')}
              data-tracking='Store.Checkout.Step1.FirstName.Fld'
              data-tracking-caller={stepperCalledFor}
            />

            {errors?.firstName && <p className={classes.inputError}> {errors?.firstName?.message}</p>}
          </Box>
          <Box className='user-details-input-wrap'>
            <Typography className='user-detail-label'>Last Name</Typography>

            <input
              type='text'
              placeholder='Enter last Name'
              className='user-details-input'
              // disabled = {isFieldDisabled('lastName')}
              {...register('lastName')}
              data-tracking='Store.Checkout.Step1.LastName.Fld'
              data-tracking-caller={stepperCalledFor}
            />

            {errors?.lastName && <p className={classes.inputError}> {errors?.lastName?.message}</p>}
          </Box>

          <Box className='user-details-input-wrap' >
            <Typography className='user-detail-label'>Gender</Typography>

            <select
              id='gender-select'
              placeholder='------'
              label='Gender'
              value={selectedGender}
              className='inputSelect'
              {...register('gender')}
              // disabled={isFieldDisabled('gender')}
              onChange={onGenderChangeHandler}
            >
              <option key={'Gender'} value='' defaultValue>
                Select a gender
              </option>
              {genderOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors?.gender && <p className={classes.inputError}> {errors?.gender?.message}</p>}

          </Box>

          <Box className='user-details-input-wrap'>
            <Typography className='user-detail-label'>Birth Date</Typography>
            <Box className='day-mnth-wrap'>
              <select
                type='number'
                placeholder='Month'
                className='inputSelect'
                value={month}
                // disabled = {isFieldDisabled('month')}
                onChange={(e) => setMonth(e.target.value)}
                data-tracking='Store.Checkout.Step1.DoB.Month.Fld'
                data-tracking-caller={stepperCalledFor}
              >
                <option value='' disabled>
                  Month
                </option>
                {months.map((m) => (
                  <option key={m} value={m}>
                    {m}
                  </option>
                ))}
              </select>

              <select
                type='number'
                placeholder='Day'
                className='inputSelect'
                value={day}
                // disabled = {isFieldDisabled('day')}
                onChange={(e) => setDay(e.target.value)}
                data-tracking='Store.Checkout.Step1.DoB.Day.Fld'
                data-tracking-caller={stepperCalledFor}
              >
                <option value='' disabled>
                  Day
                </option>
                {availableDays.map((d) => (
                  <option key={d} value={d}>
                    {d}
                  </option>
                ))}
              </select>
              <select
                type='number'
                placeholder='Year'
                className='inputSelect'
                value={year}
                // disabled = {isFieldDisabled('year')}
                onChange={(e) => setYear(e.target.value)}
                data-tracking='Store.Checkout.Step1.DoB.Year.Fld'
                data-tracking-caller={stepperCalledFor}
              >
                <option value='' disabled>
                  Year
                </option>
                {years.map((y) => (
                  <option value={y} key={y}>
                    {y}
                  </option>
                ))}
              </select>
            </Box>

            {formError.dobError !== '' && <p className={classes.inputError}>{formError.dobError}</p>}
            {errors?.dateOfBirth && <p className={classes.inputError}> {errors?.dateOfBirth?.message}</p>}
          </Box>
          <Box className='user-details-input-wrap'>
            <Typography className='user-detail-label'>Address Line 1</Typography>

            <input
              id='ship-address'
              type='text'
              placeholder='Enter Address'
              className='user-details-input'
              {...register('addressLine_1')}
              autoComplete='off'
              data-tracking='Store.Checkout.Step1.Address1.Fld'
              data-tracking-caller={stepperCalledFor}
            // disabled={isFieldDisabled('addressLine_1')}
            />
            {errors?.addressLine_1 && <p className={classes.inputError}> {errors?.addressLine_1?.message}</p>}
          </Box>
          <Box className='user-details-input-wrap'>
            <Typography className='user-detail-label'>Address Line 2</Typography>

            <input
              id='address2'
              type='text'
              placeholder='Enter Address'
              className='user-details-input'
              // disabled={isFieldDisabled('addressLine_2')}
              {...register('addressLine_2')}
              data-tracking='Store.Checkout.Step1.Address2.Fld'
              data-tracking-caller={stepperCalledFor}
            />
            {errors?.addressLine_2 && <p className={classes.inputError}> {errors?.addressLine_2?.message}</p>}
          </Box>
          <Box className='user-details-input-wrap'>
            <Typography className='user-detail-label'>City</Typography>

            <input
              id='locality'
              type='text'
              placeholder='Enter City'
              className='user-details-input'
              // disabled={isFieldDisabled('city')}
              {...register('city')}
              data-tracking='Store.Checkout.Step1.City.Fld'
              data-tracking-caller={stepperCalledFor}
            />
            {errors?.city && <p className={classes.inputError}>{errors?.city?.message}</p>}
          </Box>
          <Box className='user-details-input-wrap'>
            <Typography className='user-detail-label'>State</Typography>

            <select
              id='state'
              value={selectedState}
              type='text'
              placeholder='Enter State'
              className='inputSelect'
              style={{ width: '100%', color: '#FFFFFF' }}
              // disabled={isFieldDisabled('state')}
              {...register('state')}
              onChange={onStateChangeHandler}
              data-tracking='Store.Checkout.Step1.State.Fld'
              data-tracking-caller={stepperCalledFor}
            >
              <option key='State' value='' disabled>
                Select a State *
              </option>

              {stateData?.length > 0 &&
                stateData?.map((state) => (
                  <option key={state.state_id} value={state.stateCode}>
                    {state.name}
                  </option>
                ))}
            </select>
            {selectedState === '' && <p className={classes.inputError}>{errors?.state?.message}</p>}
          </Box>
          <Box className='user-details-input-wrap'>
            <Typography className='user-detail-label'>Postal Code</Typography>

            <input
              id='postcode'
              type='text'
              placeholder='Enter Postal Code'
              className='user-details-input'
              {...register('zipCode')}
              // disabled={isFieldDisabled('zipCode')}
              data-tracking='Store.Checkout.Step1.ZipCode.Fld'
              data-tracking-caller={stepperCalledFor}
            />
            {errors?.zipCode && <p className={classes.inputError}>{errors?.zipCode?.message}</p>}
          </Box>

          <Box className='btn-wrap'>
            <Button
              variant='contained'
              className='btn btn-primary'
              type='submit'
              data-tracking='Store.Checkout.Step1.Next.Btn'
              data-tracking-caller={stepperCalledFor}
            >
              Next
            </Button>
          </Box>
        </form>
      </Box>
    </Grid>
  )
}

export default UserDetails
