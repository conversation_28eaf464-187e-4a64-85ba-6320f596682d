import { makeStyles } from '@mui/styles'

import { MaintenanceBg } from '../ui-kit/icons/webp'

export default makeStyles((theme) => ({
  StepperModal: {
    minWidth: theme.spacing(32),
    backgroundImage: `url(${MaintenanceBg})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    backgroundPosition: '98% 100%',
    borderRadius: theme.spacing(0.625),
    top: '0 !important',
    padding: theme.spacing(0, 1),
    [theme.breakpoints.down('sm')]: {
      padding: theme.spacing(0, 1),
      minWidth: '100%'
    },
    '& .modal-close': {
      color: theme.colors.textWhite,
      marginLeft: 'auto',
      position: 'absolute',
      top: theme.spacing(0),
      right: theme.spacing(-1),
      cursor: 'pointer',
      zIndex: '5',
      '& svg': {
        fontSize: theme.spacing(1.2)
      }
    },
    '& .select-bank-card': {
      marginBottom: theme.spacing(1.125),
      padding: theme.spacing(1),
      border: '1px solid #333',
      borderRadius: '8px',
      backgroundColor: '#1c1c1c',
      color: '#fff',
      position: 'relative',
      '& .delete-icon': {
        position: 'absolute',
        right: 0,
        top: theme.spacing(-0.625),
        background: theme.colors.selectBankBg,
        '& svg': {
          fontSize: theme.spacing(1),
          color: '#FDB72E'
        }
      },
      '& .bank-details': {
        width: 'fit-content',
        [theme.breakpoints.down('md')]: {
          width: 'fit-content'
        },
        '&::before': {
          content: "''",
          position: 'absolute',
          left: '-1.25rem',
          top: '0px',
          height: '100%',
          width: '1px',
          background: '#494949',
          [theme.breakpoints.down('sm')]: {
            left: '-0.4rem'
          }
        },
        '& .bank-name': {
          fontSize: '14px',
          display: 'flex',
          justifyContent: 'space-between',
          color: '#fff',
          gap: '0.25rem',
          '& .MuiTypography-root': {
            fontWeight: '700'
          }
        },
        '& .bank-number': {
          display: 'flex',
          justifyContent: 'space-between',
          fontSize: '14px',
          height: '14px',
          color: '#606060',
          '& .MuiTypography-root': {
            fontWeight: '600'
          },
          '& .holder-name': {
            color: '#b7b7b7',
            fontWeight: '500',
            fontSize: '16px',
            lineHeight: '1.25rem',
            '&.border-right': {
              borderRight: '1px solid #b7b7b7',
              marginRight: '6px',
              paddingRight: '6px',
              height: '15px',
              display: 'flex',
              alignItems: 'center',
              paddingTop: '3.5px'
            }
          }
        }
      }
    },
    '& .themeCheckBoxWrap': {
      '& label': {
        '& .MuiFormControlLabel-label': {
          fontSize: theme.spacing(0.9),
          color: '#FFFFFF'
        },

        '& .MuiSvgIcon-root': {
          color: '#FDB72E'
        },
        '&.Mui-disabled .MuiSvgIcon-root': {
          color: 'grey'
        }
      }
    },
    '& .reedem-kyc-content': {
      padding: theme.spacing(0)
    },
    '& .leftSection': {
      width: '100%'
    },
    '& .inputWrap': {
      marginBottom: theme.spacing(1),
      '& .label': {
        fontSize: '12px',
        fontWeight: '500',
        color: '#FFFFFF'
      },
      '& .textAmount': {
        color: theme.colors.YellowishOrange
      },

      '& .skrill-email': {
        height: theme.spacing(2.5),
        border: '1px solid #494949',
        width: '100%',
        '& input': {
          height: '100%',
          color: '#FFFFFF',
          borderTopRightRadius: '0 !important',
          borderBottomRightRadius: '0 !important',
          boxShadow: theme.shadows[17],
          padding: theme.spacing(0, 0.625),
          '&:focus': {
            outline: 'none'
          }
        },
        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          border: 'none'
        }
      }
    },

    '& .equation-section': {
      border: '1px solid #494949',
      borderRadius: '10px',
      '& .equation-wrap': {
        padding: '1rem',
        '& .label': {
          color: '#707070',
          maxWidth: '445px',
          fontWeight: '500',
          [theme.breakpoints.down('sm')]: {
            fontSize: '16px'
          }
        },
        '& .equation-display': {
          top: '1rem',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          '& .equation-value': {
            background: '#3C3B3B',
            color: 'white',
            borderRadius: '5px',
            textAlign: 'center',
            padding: '0.375rem 3rem',
            fontSize: '18px',
            fontWeight: '500',
            marginBottom: '1rem',
            [theme.breakpoints.down('sm')]: {
              fontSize: '16px',
              padding: '0.375rem 1rem',
              width: '100%'
            }
          },
          '& .MuiButtonBase-root': {
            background: '#5F5F5F',
            color: '#fff',
            maxHeight: '37px'
          }
        },
        '& .MuiInputBase-root': {
          marginBottom: '0.875rem',
          border: '1px solid #757272',
          borderRadius: '6px',
          width: '100%',
          [theme.breakpoints.down('sm')]: {
            width: '100%'
          },
          '& input': {
            padding: '0.375rem 1rem',
            color: '#fff'
          },
          '& .MuiOutlinedInput-notchedOutline': {
            outline: 'none',
            border: 'none'
          }
        },
        '& .input-wrap': {
          display: 'flex',
          maxWidth: '275px',
          width: '100%',
          gap: '8px',
          [theme.breakpoints.down('sm')]: {
            width: '100%',
            maxWidth: '100%'
          }
        }
      }
    },

    '& .input-grp': {
      display: 'flex',
      // height: theme.spacing(1.875),
      fontSize: theme.spacing(0.625),
      // boxShadow: theme.shadows[17],
      '& .MuiTextField-root, & .MuiOutlinedInput-root': {
        border: '1px solid #494949',
        width: '100%',
        '& input': {
          padding: theme.spacing(0, 0.625),
          color: theme.colors.textWhite,
          borderTopRightRadius: '0 !important',
          borderBottomRightRadius: '0 !important',

          '&:focus': {
            outline: 'none'
          },
          '&:disabled': {
            color: '#fff !important',
            opacity: '1 !important',
            WebkitTextFillColor: '#fff !important'
          }
        },

        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          border: 'none'
        },
        '&:hover': {
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none'
          }
        }
      },
      '& .inner-btn': {
        minWidth: 'unset',
        color: theme.colors.textBlack,
        padding: theme.spacing(0.375, 1),
        fontSize: theme.spacing(0.875),
        background: '#FDB72E',
        fontWeight: 700,
        borderRadius: theme.spacing(0, 0.313, 0.313, 0),
        textTransform: 'uppercase',
        display: 'flex',
        alignItems: 'center'
        //minHeight: theme.spacing(2.5)
      },
      '& .MuiOutlinedInput-notchedOutline': {
        borderRadius: `${theme.spacing(0.313)} !important`,
        height: theme.spacing(2.5)
      },
      '&.max-input-wrap': {
        '& .MuiInputBase-root': {
          borderRadius: `${theme.spacing(0.25, 0, 0, 0.25)} !important`,
          height: theme.spacing(2.5)
        },
        '& .MuiOutlinedInput-notchedOutline': {
          borderRadius: `${theme.spacing(0.25, 0, 0, 0.25)} !important`,
          border: 'none'
        }
      }
    },
    '& .modal-btn': {
      padding: '6px 23px 6px 23px',
      borderRadius: '20px',
      background: '#FDB72E',
      fontSize: '14px',
      fontWeight: '600',
      lineHeight: '17.86px',
      textAlign: 'left',
      color: '#000000',
      margin: '10px',
      '&:hover': {
        background: '#FDB72E'
      }
    },
    '& .enter-otp-wrap': {
      '& p': {
        marginBottom: theme.spacing(0.0625),
        fontSize: theme.spacing(1),
        fontWeight: '500',
        textTransform: 'capitalize',
        color: theme.colors.landingFooterText,
        textAlign: 'center',
        '&.inputError': {
          color: theme.colors.error,
          fontSize: `${theme.spacing(0.8)}!important`,
          margin: '0 !important',
          lineHeight: 'normal !important',
          minHeight: '16px',
          fontWeight: '600'
        }
      }
    },
    '& .resend-otp': {
      display: 'flex',
      justifyContent: 'center',
      height: '40px',

      '& p': {
        fontSize: '14px',
        textTransform: 'capitalize',
        fontWeight: '500',
        fontFamily: 'Rajdhani',
        lineHeight: '17.86px',
        color: theme.colors.landingFooterText,
        gap: theme.spacing(0.2),
        display: 'flex',
        alignItems: 'center'
      },
      '& button': {
        background: 'transparent',
        border: 'none',
        color: theme.colors.resendText,
        fontWeight: '700',
        textTransform: 'capitalize',
        fontSize: theme.spacing(0.875),
        '&:disabled': {
          opacity: '0.5'
        }
      },
      '& span': {
        color: theme.colors.textWhite,
        paddingLeft: theme.spacing(0.2)
      }
    },

    '& .amount-input': {
      position: 'relative',
      margin: theme.spacing(2, 0, 0.9375),
      '& .MuiInputBase-root': {
        paddingRight: '0',
        background: theme.colors.inputBg,
        borderRadius: theme.spacing(0),
        '& input': {
          color: theme.colors.textWhite,

          height: theme.spacing(1.125)
        },
        '&.Mui-focused': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderRadius: theme.spacing(0.625),
            borderColor: theme.colors.YellowishOrange
          }
        },
        '& .MuiInputAdornment-root': {
          position: 'absolute',
          right: theme.spacing(1),

          '& .MuiButtonBase-root': {
            color: theme.colors.textWhite
          }
        }
      },
      '& .MuiInputBase-root-MuiOutlinedInput-root': {
        '&:hover': {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'transparent'
          }
        }
      },
      '&.opt-input': {
        display: 'flex',
        gap: theme.spacing(1),

        '& > div': {
          gap: theme.spacing(1),
          [theme.breakpoints.down('sm')]: {
            gap: theme.spacing(0.3)
          }
        },
        '&  input': {
          width: '203.67px',
          height: '32.56px',

          background: 'transparent',
          textAlign: 'center',
          border: 'none',
          fontWeight: '400',
          fontSize: '20px',
          borderBottom: '2px solid #5F5E5E',
          color: theme.colors.textWhite,
          outline: 'none'
        }
      }
    },
    '& .stepper-image-container': {
      width: '100%',
      height: '63px',
      background: '#1B181E',
      boxShadow: theme.shadows[17],
      justifyContent: 'center',
      alignItems: 'center',
      display: 'none',
      '& img': {
        width: '100px'
      }
    },
    '& .stepper-form-container': {
      width: '100%',
      borderRadius: '9px',
      height: 'auto',
      '& .MuiStepper-horizontal': {
        '& .MuiStepConnector-line': {
          borderWidth: '0.5px',
          borderColor: theme.colors.sitemapDot
        }
      },
      '& .MuiStepLabel-label': {
        fontSize: theme.spacing(0.75),
        color: `${theme.colors.YellowishOrange} !important`,
        fontWeight: theme.typography.fontWeightBold,
        lineHeight: theme.spacing(0.8),
        marginTop: theme.spacing(0.625)
      },
      '& .stepper-wrap': {
        padding: theme.spacing(2.1, 0, 1.5)
      },
      '& .payment-status-content': {
        paddingBottom: theme.spacing(2),
        '& p': {
          color: theme.colors.textWhite
        },
        '& h6': {
          color: theme.colors.textWhite,
          fontWeight: '600'
        }
      }
    },
    '& .personal-details-container': {
      // height: '33px',
      '& .personal-details-text': {
        fontFamily: 'Rajdhani',
        fontSize: '26px',
        fontWeight: '600',
        lineHeight: '33.18px',
        textAlign: 'center',
        color: '#FDB72E',
        textShadow: '0px 4px 4px #00000099'
      }
    },
    '& .small-subheader-container': {
      // height: '19px',
      '& .small-subheader-text': {
        fontFamily: 'Rajdhani',
        fontSize: '0.875rem',
        fontWeight: '700',
        lineHeight: '19px',
        color: theme.colors.textWhite,
        textAlign: 'center',
        marginBottom: '10px'
      }
    },

    //  user details styling start
    '& .user-details-section': {
      margin: theme.spacing(1, 0),
      padding: theme.spacing(0, 1),
      '& .user-details-input-wrap': {
        marginBottom: theme.spacing(0.625),
        // height: '70px',
        '& .user-detail-label': {
          height: '24px',
          fontSize: theme.spacing(0.875),
          fontWeight: '500',
          lineHeight: '23.02px',
          textAlign: 'left',
          color: '#FFFFFF'
        },
        '& .user-details-input, & .inputSelect': {
          backgroundColor: '#1D1D1D !important',
          border: '1px solid #494949',
          textShadow: '0px 4px 4px 0px #00000040',
          borderRadius: '4px',
          height: '30px',
          fontWeight: '500',
          lineHeight: '23.02px',
          outline: 'none'
        },

        '& .user-details-input': {
          width: '100%',
          fontSize: theme.spacing(0.875),
          paddingLeft: theme.spacing(0.625),
          color: theme.colors.textWhite,
          minHeight: theme.spacing(2.3125)
        },
        '& select': {
          minHeight: theme.spacing(2.3125)
        },

        '& .inputSelect': {
          fontSize: theme.spacing(0.875),
          textAlign: 'left',
          paddingLeft: '5px',
          width: '100%',
          color: theme.colors.textWhite
        },
        '& .day-mnth-wrap': {
          display: 'flex',
          alignItems: 'center',
          gap: theme.spacing(1)
        }
      }
    },

    '& .phone-input-section': {
      width: '288px',
      display: 'flex',
      padding: theme.spacing(2, 0),
      justifyContent: 'center',
      '& .custom-phone-input': {
        textAlign: 'center',
        maxWidth: theme.spacing(23.625),
        margin: '0 auto',
        '& .MuiFormControl-root': {
          width: '100%',

          margin: 0,
          color: theme.colors.textWhite,
          '& .MuiInputBase-input': {
            color: theme.colors.textWhite,
            fontSize: '12px',
            fontWeight: '600',
            // minHeight: theme.spacing(1.875),
            borderRadius: theme.spacing(0.625),
            padding: theme.spacing(0.625, 1.2),
            '&:-webkit-autofill': {
              boxShadow: 'none !important',
              borderColor: 'transparent'
            }
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: theme.colors.optBorder,
            // borderRadius: theme.spacing(0.625),
            borderRadius: '4px'
          },
          '& .MuiInputAdornment-root': {
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none'
            },
            '& .MuiSvgIcon-root': {
              right: theme.spacing(-1.5),
              top: theme.spacing(0.1875),
              color: theme.colors.textWhite
            },
            '& .MuiInputBase-input': {
              padding: 0,
              borderRadius: 0,
              display: 'flex',
              alignItems: 'center',
              minWidth: theme.spacing(3),
              '& img': {
                width: theme.spacing(2),
                height: theme.spacing(2)
              }
            }
          }
        },
        '&  p': {
          marginBottom: theme.spacing(0.0625),
          fontSize: theme.spacing(1),
          fontWeight: '600',
          textTransform: 'capitalize',
          color: theme.colors.error,
          '& .inputError': {
            color: theme.colors.error,
            fontSize: `${theme.spacing(0.8)}!important`,
            margin: '0 !important',
            lineHeight: 'normal !important',
            minHeight: '16px',
            fontWeight: '600'
          }
        }
      }
    },
    // phone number styling end

    '& .verification-code-container': {
      position: 'relative',
      // height: '33px',
      padding: theme.spacing(0),
      '& .verification-code-text': {
        fontFamily: 'Rajdhani',
        fontSize: theme.spacing(1.625),
        fontWeight: theme.typography.fontWeightBold,
        lineHeight: '33.18px',
        textAlign: 'center',
        color: '#FDB72E',
        // marginBottom: theme.spacing(1),
        textShadow: '0px 4px 4px  #00000099',
        [theme.breakpoints.down('sm')]: {
          textAlign: 'start',
          fontSize: theme.spacing(1.5)
        }
      },
      '& .timer-text': {
        position: 'absolute',
        fontSize: '1rem',
        fontWeight: '600',
        color: '#fff',
        top: '5px',
        right: '0'
      }
    },
    '& .kyc-main-content': {
      padding: theme.spacing(1, 0)
    },
    '& .MuiBox-root': {
      width: '100%',
      margin: '0'
    },
    // MODAL PAYMENT STEP-1
    '& .select-reedem-method-wrap': {
      '& .MuiTypography-heading': {
        fontSize: theme.spacing(0.875),
        fontWeight: theme.typography.fontWeightBold,
        marginBottom: theme.spacing(1),
        color: theme.colors.textWhite,
        display: 'block'
      },
      '& .MuiTypography-root': {
        fontWeight: theme.typography.fontWeightBold
      },
      '& .select-reedem-cta': {
        display: 'flex',
        alignItems: 'center',
        border: `1px solid ${theme.colors.modalTabBtnActive}`,
        borderRadius: theme.spacing(0.25),
        padding: theme.spacing(0.75, 1.0625, 0.75, 0),
        color: theme.colors.textWhite,
        textDecoration: 'none',
        fontWeight: theme.typography.fontWeightBold,
        fontSize: theme.spacing(0.75),
        boxShadow: theme.shadows[17],
        marginBottom: theme.spacing(0.5625),
        '& .reedem-icon': {
          minWidth: theme.spacing(6.25),
          textAlign: 'center',
          position: 'relative',
          marginRight: theme.spacing(2),
          '&:after': {
            position: 'absolute',
            right: theme.spacing(0.875),
            top: '50%',
            transform: 'translate(-50%, -50%)',
            background: theme.colors.modalTabBtnActive,
            content: "''",
            height: theme.spacing(1.375),
            width: '1px'
          },
          '& img': {
            width: theme.spacing(1.2),
            '&.skrill-logo': {
              width: theme.spacing(3)
            }
          }
        },
        '& .reedem-arrow': {
          marginLeft: 'auto'
        }
      },
      '& .sc-wrap': {
        display: 'flex',
        // flexDirection: 'column',
        justifyContent: 'start',
        gap: '0.25rem',
        marginTop: '1rem',
        width: 'fit-content',
        background: '#313131',
        borderRadius: '2rem',
        padding: '0.5rem',
        alignItems: 'end',
        '& .MuiTypography-root': {
          fontSize: '14px',
          color: '#A4A3A3',
          fontWeight: 600,
          lineHeight: '1'
        }
      }
    },
    // MODAL PAYMENT STEP-2
    '& .bank-account-section': {
      background: theme.colors.textBlack,
      padding: theme.spacing(1),
      borderRadius: theme.spacing(0.625),
      marginTop: theme.spacing(0.625),
      '& h4': {
        fontWeight: theme.typography.fontWeightBold,
        color: theme.colors.textWhite,
        fontSize: theme.spacing(1.1),
        marginBottom: theme.spacing(0.625)
      },
      '& .select-bank-account': {
        marginTop: theme.spacing(1, 0),
        paddingBottom: theme.spacing(3),
        '&  .css-1vjsbbl-control': {
          borderColor: theme.colors.modalTabBtnActive,
          borderRadius: theme.spacing(0.25),
          boxShadow: 'none'
        },
        '& div': {
          boxShadow: 'none',
          '&:hover': {
            borderColor: theme.colors.YellowishOrange
          }
        },
        '&  .css-1jj59y3-MenuList2': {
          padding: '0',
          borderRadius: theme.spacing(0.25)
        },
        '&  .css-3cuto7-menu': {
          borderColor: theme.colors.modalTabBtnActive,
          '&  .css-jai476-option': {
            borderBottom: `1px solid ${theme.colors.modalTabBtnActive}`
          }
        },
        '& .custom-select-content-wrap': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%',
          '& .custom-select-content': {
            display: 'flex',
            alignItems: 'center',
            gap: theme.spacing(0.625),

            '& .MuiFormControlLabel-root': {
              marginRight: 0
            },
            '& .bank-name': {
              color: theme.colors.YellowishOrange,
              fontWeight: theme.typography.fontWeightBold
            },
            '& .bank-locaion ': {
              color: theme.colors.textWhite
            },
            '& .MuiRadio-root': {
              '&.Mui-checked': {
                '& .css-hyxlzm': {
                  '& svg': {
                    '&:first-child': {
                      color: theme.colors.textWhite
                    },
                    '&:last-child': {
                      color: theme.colors.YellowishOrange
                    }
                  }
                }
              }
            },
            '& .amount-text': {
              color: theme.colors.textWhite
            }
          }
        }
        // "& .MuiFormLabel-root": {
        //   display: "none"
        // }
      }
    },
    '& .modal-paginaton-wrap': {
      padding: theme.spacing(1, 0),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      '& .MuiPaginationItem-root': {
        color: theme.colors.textWhite,
        fontWeight: theme.typography.fontWeightBold,
        '&.Mui-selected': {
          color: theme.colors.YellowishOrange,
          textDecoration: 'underline'
        }
      }
    },
    '& .add-bank-account-wrap': {
      margin: theme.spacing(0.8125, 0),
      '& .bank-account-btn': {
        width: '100%',
        textAlign: 'center',
        background: theme.colors.bankAccountBtn,
        border: `1px solid ${theme.colors.bankAccountBtn}`,
        borderRadius: theme.spacing(0.25),
        display: 'flex',
        gap: theme.spacing(0.5),
        color: theme.colors.textWhite,
        fontWeight: theme.typography.fontWeightMedium,
        minHeight: theme.spacing(1.875),
        '& .MuiSvgIcon-root': {
          color: theme.colors.YellowishOrange
        }
      }
    },
    '& .btn-wrap': {
      margin: theme.spacing(2, 0),
      gap: theme.spacing(0.625),
      display: 'flex',
      justifyContent: 'center',
      '& button': {
        width: 'auto'
      }
    },
    '& .redeem-tmf-plus-non-subscribe': {
      border: '0.93px solid #FDB72E',
      boxShadow: '0px 3.72px 3.72px 0px #FDB72E4D',
      display: 'flex',
      // flexDirection: 'column',
      alignItems: 'center',
      opacity: '1',
      borderRadius: '9.29px',
      background: 'linear-gradient(86.38deg, #1B181E 9.68%, #310247 98.47%)',
      marginBottom: '1rem',
      marginTop: '1rem',
      padding: theme.spacing(0.5),
      [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(0.25)
      },
      '& .guaranteed-content': {
        borderWidth: '0.43px',
        fontWeight: 700,
        fontSize: theme.spacing(1.18),
        background: 'linear-gradient(180deg, #F7E041 30.56%, #FFA538 77.78%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.8)
        },
        '& img': {
          width: '80px',
          margin: '-4px 5px'
        }
      },
      '& .exclusive-tmf': {
        fontWeight: '500 !important',
        fontSize: theme.spacing(1.05),
        color: theme.colors.textWhite,
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        '& .tmf-plus': {
          fontWeight: '700',
          color: ' #FDB72E'
        },
        '& .join-now': {
          fontWeight: '600',
          color: '#0077FF',
          cursor: 'pointer'
        }
      },
      '& .tmf-gif': {
        width: '5rem',
        filter: 'drop-shadow(0 0 20px #af0eff)',
        [theme.breakpoints.down('sm')]: {
          width: '4rem'
        }
      }
    },
    '& .redeem-tmf-plus-subscribe': {
      border: '0.93px solid #FDB72E',
      boxShadow: '0px 3.72px 3.72px 0px #FDB72E4D',
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
      opacity: '1',
      borderRadius: '10px',
      marginBottom: '1rem',
      marginTop: '1rem',
      padding: theme.spacing(0.5, 1),
      width: 'fit-content',
      textTransform: 'uppercase',
      marginLeft: 'auto',
      marginRight: 'auto',
      '& .guaranteed-content': {
        borderWidth: '0.43px',
        fontWeight: 700,
        fontSize: theme.spacing(1.18),
        background: 'linear-gradient(180deg, #F7E041 30.56%, #FFA538 77.78%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.8)
        },
        '& img': {
          width: '80px',
          margin: '-4px 5px'
        }
      },
      '& .exclusive-tmf': {
        fontWeight: '500 !important',
        fontSize: theme.spacing(1.05),
        color: theme.colors.textWhite,
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        '& .tmf-plus': {
          fontWeight: '700',
          color: ' #FDB72E'
        },
        '& .join-now': {
          fontWeight: '600',
          color: '#0077FF',
          cursor: 'pointer'
        }
      },
      '& .tmf-gif': {
        width: '5rem',
        filter: 'drop-shadow(0 0 20px #af0eff)',
        [theme.breakpoints.down('sm')]: {
          width: '4rem'
        }
      }
    }
  },
  promoModalWrap: {
    padding: theme.spacing(2, 1.25),
    minHeight: theme.spacing(15),
    display: 'flex',
    // alignItems: "center",
    flexDirection: 'column',
    justifyContent: 'center',
    background: theme.colors.cmsMOdalBg,
    position: 'relative',
    '& .close-btn': {
      position: 'absolute',
      right: theme.spacing(0.625),
      top: theme.spacing(0.625),
      zIndex: 2,
      cursor: 'pointer'
    },

    '& .copy-input-wrap': {
      marginBottom: theme.spacing(1),
      position: 'relative',
      '& .MuiFormControl-root': {
        background: theme.colors.inputBg,
        borderRadius: theme.spacing(0.625),
        color: theme.colors.textWhite,
        '& .MuiInputBase-root ': {
          fontWeight: theme.typography.fontWeightBold
        },
        '& .MuiOutlinedInput-input': {
          '&.Mui-disabled': {
            color: theme.colors.textWhite,
            WebkitTextFillColor: theme.colors.textWhite,
            opacity: '0.8'
          }
        }
      },
      '& .copy-icon': {
        position: 'absolute',
        right: theme.spacing(1),
        top: theme.spacing(0.625)
      }
    },
    '& .timer-text': {
      fontSize: theme.spacing(1),
      fontWeight: theme.typography.fontWeightBold
    }
  },
  modalWrapper: {
    margin: theme.spacing(1, 0)
  },
  inputError: {
    color: theme.colors.error,
    fontSize: `${theme.spacing(0.8)}!important`,
    lineHeight: 'normal !important',
    minHeight: '16px',
    fontWeight: '600'
  },
  '& .modal-section': {
    textAlign: 'center'
  },
  iframeContainer: {
    position: 'relative',
    '& .iframe-close': {
      position: 'absolute',
      top: 10,
      right: 10,
      zIndex: 10000,
      backgroundColor: '#fff',
      borderRadius: '50%',
      color: '#000',
      boxShadow: 'rgb(0 0 0 / 57%) 0px -1px 4px 2px'
    },
    '& .loader-overlay': {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      background: 'rgba(255, 255, 255, 0.7)',
      zIndex: 9999
    }
  },
  paymentStatusModal: {
    '& .MuiPaper-root': {
      maxWidth: theme.spacing(25),
      minHeight: theme.spacing(30),
      padding: theme.spacing(2),
      backgroundImage: `url(${MaintenanceBg})`,
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
      backgroundPosition: '98% 100%',
      borderRadius: theme.spacing(0.625),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    },
    '& .payment-container': {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center'
    },
    '& .status-modal-title': {
      fontSize: theme.spacing(1.5),
      fontWeight: theme.typography.fontWeightExtraBold,
      textShadow: theme.shadows[22],
      maxWidth: theme.spacing(25),
      margin: '0 auto',
      textTransform: 'uppercase',
      textAlign: 'center',
      lineHeight: theme.spacing(1.6)
    },
    '& h4': {
      fontSize: theme.spacing(1.2),
      fontWeight: theme.typography.fontWeightMedium,
      maxWidth: theme.spacing(25),
      margin: '0 auto',
      textAlign: 'center',
      lineHeight: theme.spacing(1.4),
      color: theme.colors.textWhite
    },
    '& .btn': {
      minWidth: theme.spacing(6.625)
    }
  },
  codeExpiredModalWrap: {
    '& .MuiPaper-root': {
      minWidth: theme.spacing(22),
      minHeight: theme.spacing(22),
      padding: theme.spacing(2),
      backgroundImage: `url(${MaintenanceBg})`,
      backgroundRepeat: 'no-repeat',
      backgroundSize: 'cover',
      backgroundPosition: '98% 100%',
      borderRadius: theme.spacing(0.625),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      [theme.breakpoints.down('sm')]: {
        minWidth: 'unset'
      }
    },
    '& .payment-container': {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center'
    },
    '& .status-modal-title': {
      fontSize: theme.spacing(1.5),
      fontWeight: theme.typography.fontWeightExtraBold,
      textShadow: theme.shadows[22],
      maxWidth: theme.spacing(25),
      margin: '0 auto',
      textTransform: 'uppercase',
      textAlign: 'center',
      lineHeight: theme.spacing(1.6)
    },
    '& h4': {
      fontSize: theme.spacing(1.2),
      fontWeight: theme.typography.fontWeightMedium,
      maxWidth: theme.spacing(25),
      margin: '0 auto',
      textAlign: 'center',
      lineHeight: theme.spacing(1.4),
      color: theme.colors.textWhite
    },
    '& .btn': {
      minWidth: theme.spacing(6.625)
    }
  }
}))
