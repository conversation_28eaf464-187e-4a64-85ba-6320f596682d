import React, { Fragment, useEffect, useState, lazy, Suspense, memo, useCallback } from 'react'
import {
  Button,
  Grid,
  IconButton,
  Typography,
  DialogContent,
  Box,
  Stack,
  LinearProgress
} from '@mui/material'
import moment from 'moment'
import CloseIcon from '@mui/icons-material/Close'
import { useClaimDailyBonusMutation, useGetDailyBonusMutation } from '../../reactQuery/bonusQuery'
import { toast } from 'react-hot-toast'
import { useGetProfileMutation } from '../../reactQuery'
import { useUserStore } from '../../store/useUserSlice'
import { usePortalStore } from '../../store/userPortalSlice'
import SpecialPurchaseModal from '../SpecialPurchaseModal'
import LazyImage from '../../utils/lazyImage'
import { getImageStyleProps, AspectRatios } from '../../utils/imageUtils'
import Day1 from '../../components/ui-kit/icons/png/streak-day-1.png'
import Day3 from '../../components/ui-kit/icons/png/streak-day-3.png'
import Day5 from '../../components/ui-kit/icons/png/streak-day-5.png'
import Day7 from '../../components/ui-kit/icons/png/streak-day-7.png'
import bonusBadge from '../../components/ui-kit/icons/webp/daily-bonus-badge.webp'
import StreakTimer from '../CountDownTimer/streakTimer'
import useSeon from '../../utils/useSeon'
import TagManager from 'react-gtm-module'
import DayBox from './DayBox'
import ScratchCardComponent from '../../components/ScratchCard/ScratchCardComponent'
import { getFreeSpin } from '../../utils/apiCalls'
import { useQuery } from '@tanstack/react-query'
import ModalLoader from '../ui-kit/ModalLoader'
import { CrownIcon, LockIcon } from '../ui-kit/icons/svg'
import { useNavigate } from 'react-router-dom'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'

const FreeSpinModal = lazy(() => import('../FreeSpinModal/FreeSpinModal'))
/* eslint-disable multiline-ternary */

// Memoized progress bar icons for better performance
const ProgressIcons = memo(() => (
  <Box className='progress-content'>
    <Box className='content-box'>
      <LazyImage
        src={Day1}
        alt='day-1'
        lazy={false}
        style={getImageStyleProps({
          aspectRatio: AspectRatios.SQUARE,
          width: '35px',
          height: '35px',
          objectFit: 'contain'
        })}
      />
      <Box className='label-dot'>1</Box>
    </Box>
    <Box className='content-box'>
      <LazyImage
        src={Day3}
        alt='day-3'
        lazy={false}
        style={getImageStyleProps({
          aspectRatio: AspectRatios.SQUARE,
          width: '35px',
          height: '35px',
          objectFit: 'contain'
        })}
      />
      <Box className='label-dot'>3</Box>
    </Box>
    <Box className='content-box'>
      <LazyImage
        src={Day5}
        alt='day-5'
        lazy={false}
        style={getImageStyleProps({
          aspectRatio: AspectRatios.SQUARE,
          width: '35px',
          height: '35px',
          objectFit: 'contain'
        })}
      />
      <Box className='label-dot'>5</Box>
    </Box>
    <Box className='content-box'>
      <LazyImage
        src={Day7}
        alt='day-7'
        lazy={false}
        style={getImageStyleProps({
          aspectRatio: AspectRatios.SQUARE,
          width: '35px',
          height: '35px',
          objectFit: 'contain'
        })}
      />
      <Box className='label-dot'>7</Box>
    </Box>
  </Box>
))

ProgressIcons.displayName = 'ProgressIcons'

const DailyBonus = ({ dailyBonus, resetData }) => {
  const navigate = useNavigate()
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const portalStore = usePortalStore((state) => state)
  const userSubscription = useSubscriptionStore((state) => state.userSubscription)
  const [isLoading, setIsLoading] = useState(false)

  const [enableAnimation, setEnableAnimation] = useState(false)
  const [progressBar, setProgressBar] = useState(0)
  const [remainingTime, setRemainingTime] = useState(dailyBonus?.remainingTime)
  const [claimedDays, setClaimedDays] = useState(dailyBonus?.claimedDays || [])
  const [data, setDailyBonus] = useState(dailyBonus?.streakDailyBonusDetails || [])
  const sessionId = useSeon()
  const [userBonusId, setUserBonusId] = useState(null)

  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      setRemainingTime('00:00:00')
      setClaimedDays(res?.data?.data?.claimedDays)
      setDailyBonus(res?.data?.data?.streakDailyBonusDetails)
    }
  })
  useQuery({
    queryKey: ['freespin', userBonusId],
    queryFn: ({ queryKey }) => {
      return getFreeSpin({ userBonusId: queryKey[1] })
    },
    select: (res) => res?.data?.freeSpinBonus,
    enabled: !!userBonusId,
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      if (res.length > 0) {
        portalStore.openPortal(
          () => (
            <Suspense fallback={<ModalLoader />}>
              <FreeSpinModal data={res} />
            </Suspense>
          ),
          'freeSpinModal'
        )
      }
    }
  })

  const mutationClaimDailyBonus = useClaimDailyBonusMutation({
    onSuccess: (res) => {
      TagManager.dataLayer({
        dataLayer: {
          event: 'bonus',
          bonus_type: 'daily_bonus',
          user_id: res?.data?.userWallet?.ownerId,
          gcCoin: res?.data?.userWallet?.gcCoin,
          scCoin: res?.data?.userWallet?.totalScCoin
        }
      })
      setRemainingTime('23:59:59')
      setEnableAnimation(true)
      setProgressBar((claimedDays.length / 7) * 100)
      setIsLoading(false)
      toast.success(res?.data?.message)
      localStorage.setItem('allowedUserAccess', true)

      if (res?.data?.scratchCardBonus) {
        portalStore.openPortal(
          () => (
            <ScratchCardComponent
              scratchCardBonus={res?.data?.scratchCardBonus}
              userBonusId={res?.data?.userBonusId}
              rewardType={res?.data?.rewardType}
              parentMessage={res?.data?.parentMessage}
              childMessage={res?.data?.childMessage}
            />
          ),
          'bonusStreak'
        )
      } else if (res?.data?.freeSpinUserBonusId) {
        setUserBonusId(res.data.freeSpinUserBonusId)
      } else {
        setTimeout(() => portalStore.closePortal(), 1000)
      }
      getProfileMutation.mutate()
    },
    onError: () => {
      portalStore.closePortal()
    }
  })

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (res?.data?.data?.welcomePurchaseBonusApplicable) {
        portalStore.openPortal(() => <SpecialPurchaseModal />, 'termsNConditionModal')
      }
    }
  })

  // **Effect for handling timer and API call**
  useEffect(() => {
    if (resetData !== '00:00:00') {
      if (remainingTime === '00:00:00') {
        mutationGetDailyBonus.mutate()
        return
      }

      let totalSeconds = moment.duration(dailyBonus?.resetTime).asSeconds()
      if (totalSeconds <= 0) return

      const timer = setInterval(() => {
        totalSeconds -= 1
        setRemainingTime(moment.utc(totalSeconds * 1000).format('HH:mm:ss'))
        if (totalSeconds <= 0) {
          clearInterval(timer)
          mutationGetDailyBonus.mutate()
        }
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [remainingTime, resetData])

  // **Effect for setting progress bar**
  useEffect(() => {
    setProgressBar((claimedDays.length / 7) * 100)
  }, [claimedDays])

  // Memoized claim function for better performance
  const claimDailyBonus = useCallback(
    (id) => {
      setIsLoading(true)
      const bonusId = id || data.find((x) => x.isClaimableToday)?.bonusId
      mutationClaimDailyBonus.mutate({ bonusId, cancel: false, sessionKey: sessionId, rtyuioo: sessionId === ' ' })
    },
    [data, mutationClaimDailyBonus, sessionId]
  )

  // Memoized close handler
  const handleClose = useCallback(() => {
    portalStore.closePortal()
    getProfileMutation.mutate()
  }, [portalStore, getProfileMutation])

  // // Memoized ribbon image styles
  // const ribbonImageStyles = useMemo(() =>
  //   getImageStyleProps({
  //     aspectRatio: AspectRatios.LANDSCAPE_WIDE,
  //     width: 'auto',
  //     height: 'auto',
  //     objectFit: 'contain'
  //   }), []
  // )

  const hasSubscription = userSubscription?.subscriptionDetail !== null
  const dailyBonusMultiplier = userSubscription?.subscriptionFeatureDetail?.DAILY_BONUS_MULTIPLIER
  const maxDailyBonusMultiplier = userSubscription?.subscriptionFeatureMaxValue?.DAILY_BONUS_MULTIPLIER

  return (
    <Grid className='bonus-streak-modal'>
      <Grid className='modal-close'>
        <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
          <CloseIcon />
        </IconButton>
      </Grid>
      <img src={bonusBadge} className='bonus-badge' loading='lazy' alt='bonus-badge' />
      <Box className='next-timer'>
        <button>
          <Typography variant='body1'>Next In:</Typography>
          <Typography variant='h5'>
            {' '}
            <StreakTimer
              initialTime={remainingTime}
              onTimerEnd={() => {
                if (resetData !== '00:00:00') {
                  mutationGetDailyBonus.mutate()
                }
              }}
            />
          </Typography>
        </button>
      </Box>
      <Box className='progress-main-wrap'>
        <Box className='progress-wrap'>
          <Box className='progress-bar'>
            <Stack spacing={2} sx={{ flex: 1 }}>
              <LinearProgress variant='determinate' value={progressBar} />
            </Stack>
          </Box>
          <ProgressIcons />
        </Box>
      </Box>
      <DialogContent>
        {/* TMF PLUS USER */}
        {hasSubscription ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <LazyImage src={CrownIcon} alt='Crown' lazy={false} className='lockIcon' fetchPriority='high' />
              <Typography variant='h3'>TMF PLUS {userSubscription?.subscriptionDetail?.name?.toUpperCase()}</Typography>
            </Box>
            <Typography
              variant='p'
              sx={{
                fontSize: '18px',
                fontWeight: 600,
                color: '#fff',
                textAlign: 'center',
                marginTop: '10px'
              }}
            >
              Extra {dailyBonusMultiplier}x rewards await — your Exclusive multiplier is on!
            </Typography>
            <Typography
              variant='p'
              sx={{
                fontSize: '24px',
                fontWeight: 700,
                textAlign: 'center',
                marginTop: '10px',
                background: 'linear-gradient(180deg, #FDB72E 30.56%, #BA5C25 77.78%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              ENJOY EXCLUSIVE BONUS
            </Typography>
          </Box>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            {/* TMF NON PLUS USER */}
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <LazyImage src={LockIcon} alt='Lock' lazy={false} className='lockImg' fetchPriority='high' />
              <Typography variant='h3'>EXCLUSIVE {maxDailyBonusMultiplier}X MEMBER</Typography>
            </Box>
            <Typography
              variant='h3'
              sx={{
                fontWeight: 800,
                fontSize: '35px',
                textAlign: 'center',
                background: 'linear-gradient(180deg, #F7E041 30.56%, #FFA538 77.78%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                marginLeft: '20px',
                borderImageSource: 'linear-gradient(360deg, #FDB72E 22.22%, #BA5C25 88.89%)',
                borderImageSlice: 1
              }}
            >
              {' '}
              EXCLUSIVE BONUS
            </Typography>
            <Typography
              variant='body1'
            >
              JOIN TMF PLUS and get daily login bonuses.
            </Typography>
            <Button
              variant='outlined'
              sx={{ marginTop: '10px', width: '60%', alignSelf: 'center' }}
              className='btn btn-secondary'
              onClick={() => {
                portalStore.closePortal()
                navigate('/subscriptions')
              }}
            >
              JOIN NOW
            </Button>
          </Box>
        )}

        <Grid container rowSpacing={{ xs: 1, sm: 2 }} pt={2} columnSpacing={1}>
          {data &&
            data?.map((dayData) => (
              <Fragment key={dayData?.day}>
                <DayBox data={dayData} enableAnimation={enableAnimation} claimDailyBonus={claimDailyBonus} />
              </Fragment>
            ))}
        </Grid>
      </DialogContent>
    </Grid>
  )
}

export default memo(DailyBonus)
