import React, { useEffect, useRef, useState, lazy, Suspense } from 'react'
import { Grid, Box, IconButton, Typography, Dialog } from '@mui/material'
import useStyles from './paymentStatusStyles'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import { useLocation, useNavigate } from 'react-router-dom'
import { PaymentQuery } from '../../reactQuery'
import usdIcon from '../ui-kit/icons/utils/usd-cash.webp'
import { cardCoin2 } from '../ui-kit/icons/utils'
import { usePaymentProcessStore, useUserStore } from '../../store/store'
import { usePaymentStore } from '../../store/usePaymentStore'
import Loader from '../Loader'
import { initiateTrustlyScript } from '../../pages/Store/PaymentModal/utils/trustly/initiators'
import TrustlyStatus from '../../pages/Store/PaymentModal/TrustlyStatus'
import PaymentLoader from '../Loader/PaymentLoader'
import TagManager from 'react-gtm-module'
import { paymentSocket } from '../../utils/socket'
import { loadLottieScript } from '../../utils/loadLottieScript'
import ScratchCardComponent from '../ScratchCard/ScratchCardComponent'
import { getFreeSpin } from '../../utils/apiCalls'
import { useQuery } from '@tanstack/react-query'
import ModalLoader from '../ui-kit/ModalLoader'
import { useSubscriptionStore } from '../../store/useSubscriptionStore'

const FreeSpinModal = lazy(() => import('../FreeSpinModal/FreeSpinModal'))

const PAYMENT_MODAL_STYLE_CLASS = {
  success: 'payment-status-modal',
  failed: 'payment-status-modal failed'
}

const PaymentStatus = ({
  paymentDetails: paymentDetailsProp,
  isTrustlyStatusModal,
  isCancelTrustlyWorkflow,
  status,
  isTrustly
}) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const transactionId = window.localStorage.getItem('transactionId')
  const paymentMethod = window.localStorage.getItem('paymentMethod')
  const depositTransactionId = window.localStorage.getItem('depositTransactionId')
  const userData = useUserStore((state) => state.userDetails)
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(true)
  const [showTrustlyStatus, setShowTrustlyStatus] = useState(false)
  const [showTrustlyLoader, setShowTrustlyLoader] = useState(false)
  const location = useLocation()
  const paymentDetails = location?.state?.paymentDetails || paymentDetailsProp
  const [paymentStatus, setPaymentStatus] = useState(paymentDetails?.status || status)

  const [activeScratchCard, setActiveScratchCard] = useState(false)
  const [activeFreeSpin, setActiveFreeSpin] = useState(false)
  const queryParams = new URLSearchParams(location.search)

  const paymentTransactionId = queryParams.get('transactionId')
  const merchantReference = queryParams.get('merchantReference')
  const paramsObject = Object.fromEntries(queryParams.entries())
  const paymentType = paramsObject['payment.paymentType']
  const navigate = useNavigate()
  const setPaymentDepositTransactionId = usePaymentProcessStore((state) => state.setPaymentDepositTransactionId)
  const setPaymentErrorState = usePaymentProcessStore((state) => state.setPaymentErrorState)
  const paymentErrorMessage = usePaymentProcessStore((state) => state.paymentErrorMessage)
  const timeoutRef = useRef(null)
  const paymentState = usePaymentProcessStore((state) => state.paymentState)
  const [scratchCardBonusData, setScratchCardBonus] = useState(
    paymentDetailsProp?.scratchCardBonus || paymentState?.scratchCardBonus || ''
  )
  const [userBonusIdData, setUserBonusId] = useState(paymentDetailsProp?.userBonusId || paymentState?.userBonusId || '')
  const [freeSpinBonusId, setFreeSpinBonusId] = useState(null)
  const [freeSpinBonusData, setFreeSpinBonus] = useState(
    paymentDetailsProp?.freeSpinUserBonusId || paymentState?.freeSpinUserBonusId || ''
  )
  const [rewardTypeData, setRewardType] = useState(paymentDetailsProp?.rewardType || paymentState?.rewardType || '')
  const [parentMessage, setParentMessage] = useState(
    paymentDetailsProp?.parentMessage || paymentState?.parentMessage || ''
  )
  const [childMessage, setChildMessage] = useState(paymentDetailsProp?.childMessage || paymentState?.childMessage || '')
  const { setPaymentTrustly } = usePaymentStore()
  const resetPaymentStore = usePaymentStore((state) => state.resetPaymentStore)
  const resetSubscriptionStore = useSubscriptionStore((state) => state.resetSubscriptionStore)

  const handleClose = () => {
    if (paymentMethod !== 'TRUSTLY') {
      setPaymentDialogOpen(false)
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current) // Clear timeout when popup closes
      }
      portalStore.closePortal()
      resetPaymentStore()
      resetSubscriptionStore()
    }
  }
  const { data: freeSpindata, isLoading: freeLoading } = useQuery({
    queryKey: ['freespin', freeSpinBonusId],

    queryFn: ({ queryKey }) => {
      return getFreeSpin({ userBonusId: queryKey[1] })
    },
    select: (res) => res?.data?.freeSpinBonus,
    enabled: !!freeSpinBonusId,
    refetchOnWindowFocus: false,
    onSuccess: (res) => {
      if (res.length > 0) {
        setTimeout(() => {
          portalStore.openPortal(
            () => (
              <Suspense fallback={<ModalLoader />}>
                <FreeSpinModal data={res} />
              </Suspense>
            ),
            'freeSpinModal'
          )
        }, 9000)
      }
    }
  })

  const [isPaymentPopupOpen,  setIsPaymentPopupOpen] = useState(false)

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const statuss = params.get('status')

    if (statuss === 'success' || statuss === 'failed' || statuss === 'cancelled') {
      setIsPaymentPopupOpen(true)
    }
  }, [location])

  useEffect(() => {
    if (
      activeScratchCard === true &&
      scratchCardBonusData !== undefined &&
      scratchCardBonusData != null &&
      scratchCardBonusData !== '' &&
      scratchCardBonusData !== 0
    ) {
      setActiveScratchCard(false)
      setTimeout(() => {
        portalStore.openPortal(
          () => (
            <ScratchCardComponent
              scratchCardBonus={scratchCardBonusData}
              userBonusId={userBonusIdData}
              rewardType={rewardTypeData}
            />
          ),
          'bonusStreak'
        )
      }, 9000)
    } else if (
      activeFreeSpin === true &&
      freeSpinBonusData !== undefined &&
      freeSpinBonusData != null &&
      freeSpinBonusData !== '' &&
      freeSpinBonusData !== 0 &&
      !isPaymentPopupOpen
    ) {
      setActiveFreeSpin(false)
      setFreeSpinBonusId(freeSpinBonusData)
    }
  }, [activeScratchCard, scratchCardBonusData, userBonusIdData, activeFreeSpin, freeSpinBonusData])

  // Cancel Deposit
  const setCancelDeposit = usePaymentProcessStore((state) => state.setCancelDeposit)
  const cancelDeposit = PaymentQuery.cancelDepositMutation({
    onSuccess: (res) => {
      setCancelDeposit(false)
      if (!isCancelTrustlyWorkflow) {
        window.localStorage.removeItem('transactionId')
        window.localStorage.removeItem('paymentMethod')
      }
    },
    onError: (error) => {
      setCancelDeposit(false)
      console.log('Internal Server Error', error)
    }
  })

  const setPaymentState = usePaymentProcessStore((state) => state.setPaymentState)
  const paymentCompletion = PaymentQuery.paymentProcessMutation({
    onSuccess: (res) => {
      if (res?.data?.success) {
        console.log('###PAYMENT_COMPLETION', res.data)
        TagManager.dataLayer({
          dataLayer: {
            event: 'purchase',
            amount: res?.data?.data?.amount,
            currency: 'USD',
            scCoin: res?.data?.data?.scCoin,
            gcCoin: res?.data?.data?.gcCoin,
            isFirstDeposit: res?.data?.data?.isFirstDeposit,
            totalPurchaseSum: res?.data?.data?.totalPurchaseSum,
            payment_type: 'Deposit',
            payment_method: res?.data?.data?.paymentMethod,
            transaction_id: res?.data?.data?.transactionId,
            item_id: res?.data?.data?.packageId,
            item_name: res?.data?.data?.packageName,
            user_id: userData?.userId,
            user_detail: {
              data: [
                {
                  first_name: userData?.firstName,
                  last_name: userData?.lastName,
                  email: userData?.email,
                  dob: userData?.dateOfBirth,
                  gender: userData?.gender,
                  phone: userData?.phone,
                  city: userData?.city,
                  state: userData?.stateName,
                  zipcode: userData?.zipCode,
                  country: 'US'
                }
              ]
            }
          }
        })
        if (res?.data?.data?.scratchCardBonus) {
          setScratchCardBonus(res?.data?.data?.scratchCardBonus)
          setUserBonusId(res?.data?.data?.userBonusId)
          setRewardType(res?.data?.data?.rewardType)
          setParentMessage(res?.data?.data?.parentMessage)
          setChildMessage(res?.data?.data?.childMessage)
        } else if (res?.data?.data?.freeSpinUserBonusId) {
          setFreeSpinBonusId(res?.data?.data?.freeSpinUserBonusId)
        }
        setPaymentState(res?.data?.data)
        setPaymentStatus('success')
        setPaymentDepositTransactionId({
          paymentDepositTransactionId: ''
        })
      } else {
        setPaymentStatus('failed')
        setPaymentErrorState({
          paymentError: res,
          paymentErrorMessage: res?.data?.message
        })
        setPaymentDepositTransactionId({
          paymentDepositTransactionId: ''
        })
      }

      window.localStorage.removeItem('transactionId')
      window.localStorage.removeItem('paymentMethod')
      window.localStorage.removeItem('depositTransactionId')
      setPaymentDialogOpen(true)
    },
    onError: (error) => {
      console.log('Paysafe Payment Fallback Error', error)
      setPaymentErrorState({
        paymentError: error,
        paymentErrorMessage: 'Transaction Failed'
      })
      setPaymentDepositTransactionId({
        paymentDepositTransactionId: ''
      })

      cancelDeposit.mutate({ transactionId: transactionId })
      window.localStorage.removeItem('transactionId')
      window.localStorage.removeItem('paymentMethod')
      window.localStorage.removeItem('depositTransactionId')
      setPaymentDialogOpen(true)
    }
  })

  const checkPaymentFallback = () => {
    console.log('###PAY_METHOD', paymentMethod, paymentDetails, paymentStatus)
    const payload = {
      transactionId: transactionId || paymentDetails.transactionId,
      paymentMethod: paymentDetails.paymentMethod,
      depositTransactionId: depositTransactionId
    }
    if (depositTransactionId && depositTransactionId !== '') {
      paymentCompletion.mutate(payload)
    }
  }

  useEffect(() => {
    if (
      paymentDetails?.paymentMethod === 'SKRILL' ||
      paymentDetails?.paymentMethod === 'PAY_BY_BANK'
    ) {
      setPaymentDialogOpen(false)
      checkPaymentFallback()
    } else {
      setPaymentDialogOpen(true)
    }
  }, [paymentMethod, paymentDetails])

  useEffect(() => {
    if (!isTrustlyStatusModal) {
      navigate(location.pathname)
    }
    if (paymentStatus === 'success') {
      setActiveScratchCard(true)
    } else {
      setActiveScratchCard(false)
    }
    setActiveFreeSpin(true)
    timeoutRef.current = setTimeout(() => {
      handleClose()
    }, 6000)

    return () => {
      clearTimeout(timeoutRef.current) // Cleanup on unmount
    }
  }, [])

  const initializeTrustlyPayment = PaymentQuery.savedTrustlyPaymentMutation({
    onSuccess: (res) => {
      const isTokenExpired = res?.data?.data?.tokenExpired
      const establishData = res?.data?.data?.establishData
      const trustlyOptions = res?.data?.data?.TrustlyOptions
      const isTransactionFailed = res?.data?.transactionFailed

      if (!isTokenExpired && !isTransactionFailed) {
        setShowTrustlyStatus(true)
      }

      if (isTransactionFailed || isTokenExpired) {
        setPaymentTrustly('trustlyTokenExpiredScriptLoaded', isTokenExpired)
        initiateTrustlyScript({
          callback: () => {
            setTimeout(() => {
              if (isTokenExpired) {
                Trustly.establish(establishData, trustlyOptions)
              } else {
                Trustly.selectBankWidget(establishData, trustlyOptions)
              }
            }, 1000)
          }
        })
      }
      setPaymentDepositTransactionId({
        paymentDepositTransactionId: res?.data.data.depositTransactionId
      })
    },
    onError: (error) => {
      setShowTrustlyLoader(false)
      navigate('/user/store')
      console.error('Error >>>', error)
    }
  })

  // Trustly watcher
  useEffect(() => {
    if (isTrustly) {
      paymentSocket.connect()
      if (paymentStatus === 'in-progress') {
        let updatedPath
        if (location.pathname === '/user/store/in-progress') {
          updatedPath = location.pathname.replace('/in-progress', '')
        }
        const payload = {
          transactionId: transactionId,
          redirectUrlPath: updatedPath,
          inProgressReturnURL: `${window.location.origin}${location.pathname}${location.search}`
        }
        setShowTrustlyLoader(true)
        if (paymentTransactionId) {
          if (String(paymentType) === '5') {
            payload.retryTransaction = true
            payload.merchantReference = merchantReference
          } else {
            payload.trustlyTransactionId = paymentTransactionId
          }
          initializeTrustlyPayment.mutate(payload)
        }
      } else if (location.pathname.includes('/game-play') && location.pathname.includes('/in-progress')) {
        const gameId = window.localStorage.getItem('GameID')
        const payload = {
          transactionId: transactionId,
          redirectUrlPath: `/game-play/${gameId}`,
          inProgressReturnURL: `${window.location.origin}${location.pathname}${location.search}`
        }
        setShowTrustlyLoader(true)
        if (paymentTransactionId) {
          if (String(paymentType) === '5') {
            payload.retryTransaction = true
            payload.merchantReference = merchantReference
          } else {
            payload.trustlyTransactionId = paymentTransactionId
          }
          initializeTrustlyPayment.mutate(payload)
        }
      } else if (paymentStatus === 'failed' || paymentDetails?.status === 'failed') {
        if (isCancelTrustlyWorkflow) {
          if (transactionId) cancelDeposit.mutate({ transactionId: transactionId })
        } else {
          setTimeout(() => {
            if (transactionId) cancelDeposit.mutate({ transactionId: transactionId })
            navigate('/user/store')
          }, 8000)
        }
      } else if (paymentStatus === 'success') {
        navigate('/user/store')
      }
    }
  }, [paymentMethod, paymentStatus])

  useEffect(() => {
    if (location?.state?.status === 'failed' || paymentDetails?.status === 'failed') {
      setPaymentStatus('failed')
    } else if (location?.state?.status === 'success' || paymentDetails?.status === 'success') {
      setPaymentStatus('success')
    }
  }, [location?.state?.status, paymentDetails?.status])

  useEffect(() => {
    if (paymentStatus === 'success' || paymentStatus === 'failed') {
      timeoutRef.current = setTimeout(() => {
        setPaymentDialogOpen(false)
        portalStore.closePortal()
        if (paymentMethod === 'TRUSTLY') {
          if (window.localStorage.getItem('isGameplay')) {
            window.localStorage.removeItem('paymentMethod')
            const gameId = window.localStorage.getItem('GameID')
            navigate(`/game-play/${gameId}`)
          } else {
            setTimeout(() => {
              window.localStorage.removeItem('paymentMethod')
              navigate('/user/store')
            }, 1000)
          }
        } else {
          navigate(location.pathname)
        }
      }, 8000)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [paymentStatus])

  const onAccountRefresh = (establishData, trustlyOptions) => {
    initiateTrustlyScript({
      callback: () => {
        setShowTrustlyLoader(false)
        Trustly.establish(establishData, trustlyOptions)
      },
      failure: () => {
        setShowTrustlyLoader(false)
        navigate('/user/store')
      }
    })
  }

  const getValidNumber = (value) => {
    if (!value) return 0
    const num = parseFloat(String(value).replace(/,/g, ''))
    return isNaN(num) ? 0 : num
  }

  const RenderLoader = () => {
    if (paymentMethod === 'TRUSTLY' && showTrustlyLoader) {
      return <PaymentLoader />
    }
    if (!paymentDialogOpen) {
      return <Loader />
    }
    return null
  }

  const openTrustly = () => {
    portalStore.closePortal()
  }

  useEffect(() => {
    loadLottieScript()
  }, [])

  return (
    <>
      <div id='widget-id' />
      <RenderLoader />
      {['success', 'failed', 'cancelled'].includes(paymentStatus) && (
        <Dialog className={classes.root} open={paymentDialogOpen}>
          <div className='payment-status-container'>
            <Grid className='inner-modal-header'>
              <Typography className='status-title' variant='h4'>
                Payment Status
              </Typography>
              <Grid className='modal-close'>
                <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
                  <CloseIcon />
                </IconButton>
              </Grid>
            </Grid>
            <Box className={PAYMENT_MODAL_STYLE_CLASS[paymentStatus] || ''}>
              {paymentStatus === 'success' && (
                <Grid className='payment-status-content'>
                  <Box className='success-animation'>
                    <lottie-player
                      src={`${import.meta.env.VITE_ASSETS_S3_BUCKET}/jason/success.json`}
                      background='transparent'
                      speed='1'
                      autoplay
                    />
                  </Box>
                  <Typography variant='h4'>payment successful!</Typography>
                  <Box className='amount-wrap'>
                    <Typography variant='h4'>
                      Amount Paid: <span>${paymentState?.amount || paymentDetails?.amount}</span>
                    </Typography>
                    <Box className='amount-text'>
                      <Typography variant='h5'>
                        <img src={usdIcon} alt='Coin' className='payment-accordion-detail-item-img' />
                        <span>{paymentState?.scCoin || paymentDetails?.scCoin} SC </span>
                        {getValidNumber(paymentState?.bonusSc) > 0 || getValidNumber(paymentDetails?.bonusSc) > 0 ? (
                          <span className='bonus-text'>+ {paymentState?.bonusSc || paymentDetails?.bonusSc} Bonus</span>
                        ) : null}
                      </Typography>
                      <Typography variant='h5'>
                        <img
                          src={cardCoin2}
                          style={{ height: '20px' }}
                          alt='Coin'
                          className='payment-accordion-detail-item-img'
                        />
                        <span>{paymentState?.gcCoin || paymentDetails?.gcCoin} GC </span>
                        {getValidNumber(paymentState?.bonusGc) > 0 || getValidNumber(paymentDetails?.bonusGc) > 0 ? (
                          <span className='bonus-text'>+ {paymentState?.bonusGc || paymentDetails?.bonusGc} Bonus</span>
                        ) : null}
                      </Typography>
                    </Box>
                  </Box>
                  <Typography>Thank you for Processing Your most recent Payment.</Typography>
                </Grid>
              )}
              {/* PAYMENT FAILED */}
              {paymentStatus === 'failed' && (
                <Grid className='payment-status-content'>
                  <Box className='failed-animation'>
                    <lottie-player
                      src={`${import.meta.env.VITE_ASSETS_S3_BUCKET}/jason/failed.json`}
                      background='transparent'
                      speed='1'
                      autoplay
                    />
                  </Box>
                  <Typography className='failed' variant='h4'>
                    Whoops!
                  </Typography>
                  <Typography>
                    {paymentDetails?.message ||
                      paymentErrorMessage ||
                      'Your Payment was not successfully processed. Please Try again!'}
                  </Typography>
                  {paymentDetails?.message && (
                    <a
                      href='#'
                      onClick={(e) => {
                        e.preventDefault()
                        openTrustly()
                      }}
                      style={{
                        color: '#256EFF',
                        textDecoration: 'underline',
                        fontWeight: 600,
                        fontSize: '1.25rem',
                        cursor: 'pointer',
                        marginTop: '12px',
                        display: 'inline-block'
                      }}
                    >
                      Try again!
                    </a>
                  )}
                </Grid>
              )}
              {/* PAYMENT CANCELLED */}
              {paymentStatus === 'cancelled' && (
                <Grid className='payment-status-content'>
                  <Box className='failed-animation'>
                    <lottie-player
                      src={`${import.meta.env.VITE_ASSETS_S3_BUCKET}/jason/failed.json`}
                      background='transparent'
                      speed='1'
                      autoplay
                    />
                  </Box>
                  <Typography className='failed' variant='h4'>
                    Cancelled
                  </Typography>
                  <Typography>Your Payment was successfully cancelled.</Typography>
                </Grid>
              )}
            </Box>
          </div>
        </Dialog>
      )}

      {showTrustlyStatus && (
        <TrustlyStatus
          isRenderInPaymentStatus
          onAccountRefresh={onAccountRefresh}
          setShowTrustlyLoader={setShowTrustlyLoader}
        />
      )}
    </>
  )
}

export default PaymentStatus
