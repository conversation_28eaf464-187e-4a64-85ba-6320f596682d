import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>, Button,
  DialogContent,
  TextField,
  Box,
  MenuItem
} from '@mui/material'
import useStyles from './CancelTMFSubscription.styles'
import CloseIcon from '@mui/icons-material/Close'
import { usePortalStore } from '../../store/userPortalSlice'
import subscriptionQuery from '../../reactQuery/subscriptionQuery'
import toast from 'react-hot-toast'

const CancelTMFSubscription = ({ subscriptionId, refetchSubscriptions }) => {
  const classes = useStyles()
  const portalStore = usePortalStore((state) => state)
  const [selectedReason, setSelectedReason] = useState('')
  const [cancellationReason, setCancellationReason] = useState('')

  const reasons = [
    'No longer interested in online gambling',
    'Financial reasons / too expensive',
    'Dissatisfied with bonuses or promotions',
    'Unhappy with game selection',
    'Withdrawal issues or delays',
    'Customer service dissatisfaction',
    'Privacy or security concerns',
    'Other'
  ]

  const cancelSubscription = subscriptionQuery.useCancelSubscriptionMutation({
    onSuccess: (res) => {
      if (res?.data?.success) {
        toast.success('Your subscription has been canceled successfully.')
        refetchSubscriptions()
        portalStore.closePortal()
      }
    },
    onError: (error) => {
      toast.error('An error occurred while canceling your subscription. Please try again later.')
      console.log('error', error)
      portalStore.closePortal()
    }
  })

  const handleClose = () => {
    portalStore.closePortal()
  }

  const handleConfirm = () => {
    const reasonToSend = selectedReason === 'Other' ? cancellationReason : selectedReason

    if (!reasonToSend) {
      toast.error('Please select or enter a cancellation reason.')
      return
    }

    cancelSubscription.mutate({
      userSubscriptionId: subscriptionId,
      cancellationReason: reasonToSend
    })
  }

  return (
    <>
      <Grid className={classes.innerModalHeader}>
        <Typography variant='h4'>Confirmation</Typography>
        <Grid className={classes.modalClose}>
          <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
            <CloseIcon />
          </IconButton>
        </Grid>
      </Grid>

      <DialogContent className={classes.paymentStatusModal}>
        <Box className={classes.paymentStatusContent}>
          <Typography variant='h6'>Are you sure you want to cancel your subscription?</Typography>
          <Typography variant='h6'>Before You cancel, please specify your reason of cancellation below.</Typography>
          <TextField
            select
            fullWidth
            variant='outlined'
            className={classes.textField}
            value={selectedReason}
            onChange={(e) => setSelectedReason(e.target.value)}
            placeholder='Select a reason'
          >
            {reasons.map((reason) => (
              <MenuItem className={classes.menuItem} key={reason} value={reason}>
                {reason === 'Other' ? 'Other (Please specify)' : reason}
              </MenuItem>
            ))}
          </TextField>

          {selectedReason === 'Other' && (
            <TextField
              className={classes.textField}
              fullWidth
              variant='outlined'
              placeholder='Reason for Cancellation...'
              value={cancellationReason}
              onChange={(e) => setCancellationReason(e.target.value)}
              multiline
              maxRows={3}
            />
          )}

          <Box className={classes.buttonsWrapper}>
            <Button
              variant='contained'
              className='btn btn-primary'
              onClick={() => handleConfirm()}
            >
              Cancel
            </Button>
            <Button
              variant='outlined'
              onClick={handleClose}
              className='btn btn-secondary'
            >
              Back
            </Button>
          </Box>
        </Box>
      </DialogContent>
    </>
  )
}

export default CancelTMFSubscription
