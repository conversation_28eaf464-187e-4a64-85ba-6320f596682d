import clone from 'just-clone'

export const formatValueWithK = (amount) => {
  if (amount) {
    let finalAmount
    if (amount >= 1000000000) {
      finalAmount = amount / 1000000000
      return finalAmount % 1 !== 0 ? `${finalAmount.toFixed(2)} B` : `${finalAmount} B`
    }

    if (amount < 1000) {
      finalAmount = amount
      return finalAmount % 1 !== 0 ? finalAmount.toFixed(2) : finalAmount
    } else {
      finalAmount = amount / 1000

      return finalAmount % 1 !== 0 ? `${finalAmount.toFixed(2)} K` : `${finalAmount} K`
    }
  }
  return 0
}

export const isValidPhoto = (fileName) => {
  var allowed_extensions = new Array('jpg', 'png', 'webp')
  var file_extension = fileName.split('.').pop().toLowerCase()
  for (var i = 0; i <= allowed_extensions.length; i++) {
    if (allowed_extensions[i] === file_extension) {
      return true // valid file extension
    }
  }

  return false
}
export const formatValueWithB = (amount) => {
  if (amount) {
    let finalAmount
    if (amount >= 1000000000) {
      finalAmount = amount / 1000000000
      return finalAmount % 1 !== 0 ? `${finalAmount.toFixed(2)} B` : `${finalAmount} B`
    }
  }
  return amount
}
export const numberWithCommas = (number) => {
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export const toDecimals = (value) => {
  const valueToFixed = (Math.round(value * 100) / 100).toFixed(2)

  const valueWithCommas = numberWithCommas(valueToFixed)

  return valueWithCommas
}

export const blockEvent = (e) => {
  e.preventDefault()
  e.stopPropagation()
}

export const goToFAQ = () => window.open('')
export const goToContactForm = () => window.open('')

export const copyToClipBoard = (text) => {
  if (window.clipboardData && window.clipboardData.setData) {
    // Internet Explorer-specific code path to prevent textarea being shown while dialog is visible.
    return window.clipboardData.setData('Text', text)
  }
  if (document.queryCommandSupported && document.queryCommandSupported('copy')) {
    const textarea = document.createElement('textarea')
    textarea.textContent = text
    textarea.style.position = 'fixed' // Prevent scrolling to bottom of page in Microsoft Edge.
    document.body.appendChild(textarea)
    textarea.select()
    try {
      return document.execCommand('copy') // Security exception may be thrown by some browsers.
    } catch (ex) {
      console.warn('Copy to clipboard failed.', ex)
      return false
    } finally {
      document.body.removeChild(textarea)
    }
  }
}

export const navigateToCMS = (cmsData, slug, navigate, id = '') => {
  const cmsId = cmsData?.find((cms) => cms?.slug === slug)?.cmsPageId
  cmsId && navigate(id ? `/cms/${cmsId}/${slug}#${id}` : `/cms/${cmsId}/${slug}`)
}

export const formatNumberToM = (number) => {
  if (typeof number !== 'number') {
    throw new Error('Input must be a number.')
  }

  // Check if the number is greater than or equal to 1 million
  if (number >= 1000000) {
    const millions = Math.floor(number / 1000000) // Get the number of millions
    const remainder = number % 1000000 // Get the remainder

    // Check if there is a remainder (e.g., 1000001 should be '1.1M')
    if (remainder > 0) {
      const formattedNumber = (millions + remainder / 1000000).toFixed(1)
      return `${formattedNumber}M`
    } else {
      return `${millions}M`
    }
  }

  // If the number is less than 1 million, return it as is
  return number.toString()
}
export const setWithExpiry = (key) => {
  const now = new Date()

  // `item` is an object which contains the original value
  // as well as the time when it's supposed to expire
  const item = now.getTime()
  localStorage.setItem(key, item)
}

export const formatDate = (date, format) => {
  var d = new Date(date),
    month = '' + (d.getMonth() + 1),
    day = '' + d.getDate(),
    year = d.getFullYear()
  if (month.length < 2) month = '0' + month
  if (day.length < 2) day = '0' + day

  if (format && format === 'dd/mm/yyyy') {
    return [day, month, year].join('/')
  }

  return [year, month, day].join('-')
}

export const commonDateTimeFormat = {
  date: 'MM/DD/YYYY',
  dateWithTime: 'MM/DD/YYYY hh:mm A'
}

export const tournamentDateFormat = {
  dateWithTime: 'DD MMM YYYY hh:mm A'
}

export const formatCommonNumber = (coin) => {
  if (typeof coin === 'string') {
    const parsedNumber = parseFloat(coin)
    if (!isNaN(parsedNumber)) {
      const formattedNumber = parsedNumber.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
      return formattedNumber
    }
  } else if (typeof coin === 'number') {
    const formattedNumber = coin.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
    return formattedNumber
  }
  return coin
}

export const generateRandomToken = (length) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const tokenArray = []

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length)
    tokenArray.push(characters.charAt(randomIndex))
  }

  return tokenArray.join('')
}

export const capitalizeText = (text) => {
  const words = text?.split(' ')
  const capitalizedWords = words?.map((word) => {
    if (word?.length === 0) {
      return ''
    }
    return word[0].toUpperCase() + word.slice(1).toLowerCase()
  })
  const capitalizedText = capitalizedWords.join(' ')

  return capitalizedText
}

export const getBrowserType = () => {
  const test = (regexp) => {
    return regexp.test(navigator.userAgent)
  }

  if (test(/opr\//i) || !!window.opr) {
    return 'Opera'
  } else if (test(/edg/i)) {
    return 'Microsoft Edge'
  } else if (test(/chrome|chromium|crios/i)) {
    return 'Google Chrome'
  } else if (test(/firefox|fxios/i)) {
    return 'Mozilla Firefox'
  } else if (test(/safari/i)) {
    return 'Apple Safari'
  } else if (test(/trident/i)) {
    return 'Microsoft Internet Explorer'
  } else if (test(/ucbrowser/i)) {
    return 'UC Browser'
  } else if (test(/samsungbrowser/i)) {
    return 'Samsung Browser'
  } else {
    return 'Unknown browser'
  }
}

export const getOSAndBrowser = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const platform = navigator.platform ? navigator.platform.toLowerCase() : 'unknown'

  let os = 'Unknown OS'
  if (platform.includes('mac')) os = 'MacOS'
  else if (platform.includes('win')) os = 'Windows'
  else if (platform.includes('linux')) os = 'Linux'
  else if (/android/.test(userAgent)) os = 'Android'
  else if (/iphone|ipad|ipod/.test(userAgent)) os = 'iOS'

  let browser = 'Unknown Browser'
  if (/chrome/.test(userAgent) && !/edg|opr|brave/.test(userAgent)) {
    browser = 'Chrome'
  } else if (/safari/.test(userAgent) && !/chrome|crios|fxios/.test(userAgent)) {
    browser = 'Safari' // Ensures Chrome on iOS (CriOS) is excluded
  } else if (/firefox|fxios/.test(userAgent)) {
    browser = 'Firefox'
  } else if (/edg/.test(userAgent)) {
    browser = 'Edge'
  } else if (/opr|opera/.test(userAgent)) {
    browser = 'Opera'
  } else if (/msie|trident/.test(userAgent)) {
    browser = 'Internet Explorer'
  }

  return { os, browser }
}

export const handlePasswordPaste = (event) => {
  event.preventDefault()
}

export function formatPriceWithCommas(price) {
  if (price) {
    const priceString = price.toString()
    const parts = priceString.split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    return parts.join('.')
  }
  return 0
}

export const getAllSCValues = (wheelConfiguration) => {
  if (!wheelConfiguration || wheelConfiguration.length === 0) {
    return []
  }
  return wheelConfiguration.map((wheel) => formatValueWithK(wheel.sc))
}

export const getAllGCValues = (wheelConfiguration) => {
  if (!wheelConfiguration || wheelConfiguration.length === 0) {
    return []
  }
  return wheelConfiguration.map((wheel) => formatValueWithK(wheel.gc))
}

export const calculateRemainingTime = (claimedAt) => {
  const now = new Date().getTime()
  const targetDate = claimedAt

  const targetTime = new Date(targetDate)
  // targetTime.setDate(targetTime.getDate() + 1);

  const timeDifference = targetTime - now

  if (timeDifference <= 0) return { hours: '00', minutes: '00', seconds: '00' }
  let seconds = Math.floor((timeDifference / 1000) % 60)
  let minutes = Math.floor((timeDifference / 1000 / 60) % 60)
  let totalHours = Math.floor(timeDifference / (1000 * 3600))

  if (totalHours < 10) {
    totalHours = '0' + totalHours
  }
  if (minutes < 10) {
    minutes = '0' + minutes
  }
  if (seconds < 10) {
    seconds = '0' + seconds
  }

  return { hours: totalHours, minutes, seconds }
}

function unionBy(arr1 = [], arr2 = [], key) {
  const map = new Map()
  ;[...arr1, ...arr2].forEach((item) => {
    if (item && key in item) {
      map.set(item[key], item)
    }
  })
  return [...map.values()]
}

// Utility: deep merge objects (simplified for this use-case)
function deepMerge(obj1, obj2) {
  const result = clone(obj1)
  for (const key in obj2) {
    if (obj2[key] && typeof obj2[key] === 'object' && !Array.isArray(obj2[key])) {
      result[key] = deepMerge(result[key] || {}, obj2[key])
    } else {
      result[key] = obj2[key]
    }
  }
  return result
}

// Dynamic merge helper function for games
// export function dynamicMerge(arr1, arr2, idKey, subKey = null, subIdKey = null) {
//   // Helper function to merge sub-level arrays
//   function mergeSubArrays(subArr1, subArr2) {
//     return _.unionBy(
//       _.map(subArr1, (item) => {
//         const matchingItem = _.find(subArr2, { [subIdKey]: item[subIdKey] })
//         return matchingItem ? _.merge({}, item, matchingItem) : item
//       }),
//       subArr2,
//       subIdKey
//     )
//   }

//   // Main merging logic
//   const merged = _.unionBy(
//     _.map(arr1, (item) => {
//       const matchingItem = _.find(arr2, { [idKey]: item[idKey] })
//       if (matchingItem) {
//         const mergedItem = _.merge({}, item, matchingItem)
//         // If subKey and subIdKey are provided, merge sub-arrays
//         if (subKey && subIdKey && Array.isArray(item[subKey]) && Array.isArray(matchingItem[subKey])) {
//           mergedItem[subKey] = mergeSubArrays(item[subKey], matchingItem[subKey])
//         }
//         return mergedItem
//       }
//       return item
//     }),
//     arr2,
//     idKey
//   )

//   return merged
// }

export function dynamicMerge(arr1, arr2, idKey, subKey = null, subIdKey = null) {
  // Merge sub-arrays if needed
  function mergeSubArrays(subArr1, subArr2) {
    const mergedSub = subArr1.map((item) => {
      const match = subArr2.find((sub) => sub[subIdKey] === item[subIdKey])
      return match ? deepMerge(item, match) : item
    })
    return unionBy(mergedSub, subArr2, subIdKey)
  }

  const merged = arr1.map((item) => {
    const match = arr2.find((obj) => obj[idKey] === item[idKey])
    if (match) {
      const mergedItem = deepMerge(item, match)

      // Merge sub-arrays if keys provided
      if (subKey && subIdKey && Array.isArray(item[subKey]) && Array.isArray(match[subKey])) {
        mergedItem[subKey] = mergeSubArrays(item[subKey], match[subKey])
      }

      return mergedItem
    }
    return item
  })

  return unionBy(merged, arr2, idKey)
}

// Update FavoriteGames
export const updateFavoriteGames = (ids, games) => {
  return games?.map((game) => {
    if (ids.includes(game.masterCasinoGameId)) {
      return { ...game, FavoriteGames: true } // Update FavoriteGames to true
    }
    return game // Keep the game unchanged if the ID is not present
  })
}

export const formatAmount = (amount) => {
  if (amount === null) return ''
  return Number.isInteger(amount) ? amount : amount.toFixed(2)
}

export const formatDiscountAmount = (amount, discountPercentage) => {
  if (amount === null) return ''
  const discountedAmount = amount * (1 - discountPercentage / 100)
  return Number.isInteger(discountedAmount)
    ? discountedAmount
    : discountedAmount.toFixed(2)
}

// Preload image to solve LCP

export const currectYear = new Date().getFullYear()

// Jackpot table
export function formatToReadableDateTime(isoString) {
  const date = new Date(isoString)

  const pad = (n) => String(n).padStart(2, '0')

  const year = date.getFullYear()
  const month = pad(date.getMonth() + 1) // Months are 0-based
  const day = pad(date.getDate())
  const hours = pad(date.getHours())
  const minutes = pad(date.getMinutes())

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

export function getTimeUntilNextUTC (utcTimeStr) {
  // Parse '9.30 AM UTC' into hours and minutes
  const [time, meridiem] = utcTimeStr.split(' ')
  const [hourStr, minuteStr] = time.split('.')

  let hours = parseInt(hourStr, 10)
  const minutes = parseInt(minuteStr, 10)

  // Convert to 24-hour format if needed
  if (meridiem.toUpperCase() === 'PM' && hours !== 12) hours += 12
  if (meridiem.toUpperCase() === 'AM' && hours === 12) hours = 0

  // Current UTC time
  const now = new Date()
  const target = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), hours, minutes))

  // If target time already passed today, move to tomorrow
  if (target <= now) {
    target.setUTCDate(target.getUTCDate() + 1)
  }

  const diffInMs = target - now
  const totalSeconds = Math.floor(diffInMs / 1000)
  const hrs = Math.floor(totalSeconds / 3600)
  const mins = Math.floor((totalSeconds % 3600) / 60)
  const secs = totalSeconds % 60

  return [hrs, mins, secs]
    .map(num => num.toString().padStart(2, '0'))
    .join(':')
}
