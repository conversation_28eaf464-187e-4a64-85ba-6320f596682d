import debounce from '@mui/utils/debounce'
import isEqualLib from 'fast-deep-equal'
import clone from 'just-clone'

// ✅ Safe deep clone function
export const cloneDeep = (obj) => {
    return clone(obj)
}
//  debounce

export const debounceFn = (func, wait) => {
    return debounce(func, wait)
};

//  isNull
export const isNull = (value) => value === null

//  isUndefined
export const isUndefined = (value) => value === undefined

//  isEqual (deep equality check)
export const isEqual = (a, b) => isEqualLib(a, b)

export default {
    cloneDeep,
    debounce,
    isNull,
    isUndefined,
    isEqual
};
