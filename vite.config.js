import react from '@vitejs/plugin-react'
import { defineConfig, loadEnv } from 'vite'

export default defineConfig(({ mode }) => {
   
  // Load env variables for the current mode (e.g., 'development' or 'production')
  const env = loadEnv(mode, process.cwd(), '') // eslint-disable-line
  

  return {
    plugins: [react()],
    server: {
      allowedHosts: 'all',
      watch: {
        usePolling: true
      },
      host: true, // needed for the Docker Container port mapping to work
      strictPort: true,
      port: 8080 // you can replace this port with any port
    },
    preview: {
      port: 8005
    },
    base: '/',
    build: {
      input: {
        app: './index.html'
      },
      // sourcemap needs to be false on stage,preprod,prod
      sourcemap: env.VITE_NODE_ENV !== 'production',
      assetsDir: 'assets', // Static assets directory
      manifest: true, // Generate manifest for advanced caching
      chunkSizeWarningLimit: 1000, // Increase warning limit but still optimize
      rollupOptions: {
        output: {
          entryFileNames: 'assets/[name].[hash].js',
          chunkFileNames: 'assets/[name].[hash].js',
          assetFileNames: 'assets/[name].[hash].[ext]',
          manualChunks: {
            // Vendor chunks for better caching
            'react-vendor': ['react', 'react-dom', 'react-router-dom'],
            'mui-vendor': ['@mui/material', '@mui/icons-material', '@emotion/react', '@emotion/styled'],
            'utils-vendor': ['axios', 'moment', 'zustand'],
            'ui-vendor': ['swiper', 'react-image', 'react-intersection-observer'],

            // Landing page specific chunks
            'landing-components': [
              './src/pages/Landing/LandingHeader/index.jsx',
              './src/pages/Landing/LandingBanner/index.jsx',
              './src/pages/Landing/SocailSection/index.jsx',
              './src/pages/Landing/ProviderSection/index.jsx'
            ],

            // SEO Landing Pages chunk (public pages)
            'seo-pages': [
              './src/components/SeoLandingPages/GamesPage.jsx',
              './src/components/SeoLandingPages/BlogSection/BlogPage.jsx',
              './src/components/SeoLandingPages/FAQ Section/faq.jsx',
              './src/components/SeoLandingPages/ContactUs/ContactUsPage.jsx',
              './src/components/SeoLandingPages/AboutUs/AboutUsPage.jsx',
              './src/components/SeoLandingPages/BlogSection/DynamicBlog.jsx'
            ],

            // User Account & Profile chunk (private pages)
            'account-pages': [
              './src/pages/Accounts/components/ProfileSection.jsx',
              './src/pages/Accounts/components/DynamicVipForm/index.jsx'
            ],

            // Utility & Feature Pages chunk
            'feature-pages': [
              './src/pages/GeoBlock/index.jsx',
              './src/pages/ComingSoon/index.jsx',
              './src/pages/NotAvailable/index.jsx',
              './src/pages/ReferPage/index.jsx',
              './src/pages/HallOfFame/index.jsx',
              './src/pages/ResponsibleGambling/index.jsx',
              './src/pages/Jackpot/index.jsx'
            ]
          }
        }
      },
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: env.VITE_NODE_ENV === 'production',
          drop_debugger: env.VITE_NODE_ENV === 'production',
          passes: 2,
          pure_funcs: env.VITE_NODE_ENV === 'production' ? ['console.log', 'console.info', 'console.debug'] : []
        },
        mangle: true,
        format: { comments: false }
      }
    }
  }
})
