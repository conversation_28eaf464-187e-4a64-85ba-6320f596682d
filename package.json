{"name": "theMoneyFactory", "private": true, "version": "0.0.0", "type": "module", "scripts": {"prepare": "husky install", "dev": "vite", "build": "vite build", "preview": "npm run build & vite preview", "lint": "./node_modules/.bin/standard 'src/**/*.{js,jsx}'", "lint:fix": "eslint \"./src/**/*.{js,jsx,ts,tsx}\" --fix", "sync-icons": "node scripts/sync_icons.js", "analyze": "source-map-explorer 'dist/assets/*.js'"}, "dependencies": {"@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@hookform/resolvers": "3.3.1", "@material-ui/core": "^4.12.4", "@mui/icons-material": "5.14.18", "@mui/material": "5.14.18", "@mui/styled-engine-sc": "6.0.0-alpha.6", "@mui/styles": "5.14.13", "@mui/x-date-pickers": "^6.18.4", "@react-oauth/google": "0.11.1", "@sumsub/websdk": "^2.0.2", "@tanstack/react-query": "4.32.6", "@vitejs/plugin-react": "4.0.3", "axios": "1.4.0", "fast-deep-equal": "^3.1.3", "html-react-parser": "^5.0.7", "just-clone": "^6.2.0", "moment": "2.29.4", "object-to-formdata": "4.5.1", "react": "18.2.0", "react-apple-login": "^1.1.6", "react-dom": "18.2.0", "react-dropzone": "14.2.3", "react-error-boundary": "^4.1.2", "react-gtm-module": "^2.0.11", "react-helmet-async": "^2.0.5", "react-hook-form": "7.46.1", "react-hot-toast": "2.4.1", "react-image": "^4.1.0", "react-intersection-observer": "^9.16.0", "react-lazy-load": "^4.0.1", "react-otp-input": "^3.1.1", "react-qr-code": "^2.0.15", "react-router-dom": "6.14.2", "react-scratchcard-v2": "^1.1.1", "react-select": "5.7.4", "socket.io-client": "4.7.2", "swiper": "^11.0.5", "vite": "4.4.9", "yup": "1.2.0", "zustand": "4.4.1"}, "devDependencies": {"@types/react": "18.2.15", "@types/react-dom": "18.2.7", "css-minimizer-webpack-plugin": "^7.0.0", "eslint": "^9.27.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.3", "eslint-plugin-unused-imports": "^4.1.4", "humps": "2.0.1", "husky": "7.0.4", "lint-staged": "11.0.0", "prettier": "2.8.8", "sass": "1.64.2", "source-map-explorer": "^2.5.3", "standard": "16.0.3", "terser": "^5.42.0", "vite-plugin-mkcert": "^1.17.1", "webpack-bundle-analyzer": "^4.10.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "npm run lint:fix"}, "standard": {"env": ["jest", "es6", "browser"], "ignore": ["build/*", "jquery-3.7.1.js"]}}